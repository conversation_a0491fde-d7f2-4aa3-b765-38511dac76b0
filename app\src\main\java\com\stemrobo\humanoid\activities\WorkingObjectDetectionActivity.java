package com.stemrobo.humanoid.activities;

import android.Manifest;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.camera.view.PreviewView;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.lifecycle.ViewModelProvider;

import com.google.mlkit.vision.face.Face;
import com.stemrobo.humanoid.R;
import com.stemrobo.humanoid.vision.CameraXViewModel;
import com.stemrobo.humanoid.vision.FaceBoxOverlay;
import com.stemrobo.humanoid.vision.FaceDetectionManager;
import com.stemrobo.humanoid.vision.yolo.YoloClassifier;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class WorkingObjectDetectionActivity extends AppCompatActivity implements
    FaceDetectionManager.FaceDetectionCallback {

    private static final String TAG = "WorkingObjectDetection";
    private static final int CAMERA_PERMISSION_REQUEST_CODE = 100;

    // UI Components
    private TextView resultTextView;
    private PreviewView cameraPreview;
    private FaceBoxOverlay faceBoxOverlay;
    private LinearLayout titleBar;
    private LinearLayout bottomPanel;
    private ImageButton fullScreenToggleButton;
    private ImageButton exitFullScreenButton;

    // Vision Components
    private YoloClassifier yoloClassifier;
    private FaceDetectionManager faceDetectionManager;
    private CameraXViewModel cameraXViewModel;

    // Smart Greeting Integration
    private com.stemrobo.humanoid.behaviors.SmartGreetingManager smartGreetingManager;
    private com.stemrobo.humanoid.communication.ESP32CommunicationManager communicationManager;

    // Full-screen state
    private boolean isFullScreenMode = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_working_object_detection);

        initViews();
        initVisionSystem();

        if (checkCameraPermission()) {
            startFaceDetection();
        } else {
            requestCameraPermission();
        }
    }

    private void initViews() {
        resultTextView = findViewById(R.id.result_text_view);
        cameraPreview = findViewById(R.id.camera_preview);
        faceBoxOverlay = findViewById(R.id.face_box_overlay);
        titleBar = findViewById(R.id.title_bar);
        bottomPanel = findViewById(R.id.bottom_panel);
        fullScreenToggleButton = findViewById(R.id.fullscreen_toggle_button);
        exitFullScreenButton = findViewById(R.id.exit_fullscreen_button);

        // Set up full-screen toggle button
        if (fullScreenToggleButton != null) {
            fullScreenToggleButton.setOnClickListener(v -> toggleFullScreenMode());
        }

        // Set up exit full-screen button
        if (exitFullScreenButton != null) {
            exitFullScreenButton.setOnClickListener(v -> exitFullScreenMode());
            exitFullScreenButton.setVisibility(View.GONE); // Initially hidden
        }
    }

    private void initVisionSystem() {
        // Initialize CameraX ViewModel
        cameraXViewModel = new ViewModelProvider(this).get(CameraXViewModel.class);

        // Initialize face detection manager
        faceDetectionManager = new FaceDetectionManager(this, this);
        faceDetectionManager.setPreviewView(cameraPreview, faceBoxOverlay);
        faceDetectionManager.setCallback(this);

        // Initialize YOLO classifier (optional)
        try {
            yoloClassifier = new YoloClassifier(getAssets(), "yolov4-416.tflite", "labelmap.txt");
            Log.d(TAG, "YoloClassifier initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing YoloClassifier", e);
        }

        // Initialize Smart Greeting Manager for full-screen object detection
        initializeSmartGreeting();

        resultTextView.setText("Face detection with expression analysis ready!\nFace count and closest face expression will be displayed.\nSmart Greeting active in full-screen mode!");
    }

    /**
     * Initialize Smart Greeting Manager for full-screen object detection mode
     */
    private void initializeSmartGreeting() {
        try {
            // Initialize ESP32 Communication Manager
            communicationManager = com.stemrobo.humanoid.communication.ESP32CommunicationManager.getInstance();
            communicationManager.initialize(this);

            // Initialize Smart Greeting Manager
            smartGreetingManager = com.stemrobo.humanoid.behaviors.SmartGreetingManager.getInstance();
            smartGreetingManager.initialize();

            // Load settings from SharedPreferences
            android.content.SharedPreferences prefs = getSharedPreferences("com.stemrobo.humanoid_preferences", MODE_PRIVATE);
            smartGreetingManager.loadSettingsFromPreferences(prefs);

            // Set callback for Smart Greeting events
            smartGreetingManager.setCallback(new com.stemrobo.humanoid.behaviors.SmartGreetingManager.IntegrationCallback() {
                @Override
                public void onIntegrationStatusChanged(boolean isActive) {
                    Log.d(TAG, "Smart Greeting integration status: " + isActive);
                }

                @Override
                public void onGreetingPerformed(String greetingType) {
                    runOnUiThread(() -> {
                        resultTextView.setText("Smart Greeting: " + greetingType + "\nFull-screen object detection active!");
                        Log.d(TAG, "Smart Greeting performed: " + greetingType);
                    });
                }

                @Override
                public void onCameraActivationRequested() {
                    Log.d(TAG, "Smart Greeting requested camera activation (already active)");
                }

                @Override
                public void onFaceDetectionRequested() {
                    Log.d(TAG, "Smart Greeting requested face detection (already active)");
                }

                @Override
                public void onCameraDeactivationRequested() {
                    Log.d(TAG, "Smart Greeting requested camera deactivation (ignored in full-screen mode)");
                }
            });

            Log.d(TAG, "Smart Greeting Manager initialized for full-screen object detection");

        } catch (Exception e) {
            Log.e(TAG, "Error initializing Smart Greeting Manager: " + e.getMessage());
        }
    }

    private boolean checkCameraPermission() {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA)
                == PackageManager.PERMISSION_GRANTED;
    }

    private void requestCameraPermission() {
        ActivityCompat.requestPermissions(this,
                new String[]{Manifest.permission.CAMERA},
                CAMERA_PERMISSION_REQUEST_CODE);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                         @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == CAMERA_PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                startFaceDetection();
            } else {
                Toast.makeText(this, "Camera permission is required for face detection",
                             Toast.LENGTH_LONG).show();
                resultTextView.setText("Camera permission denied. Cannot start face detection.");
            }
        }
    }

    private void startFaceDetection() {
        resultTextView.setText("Camera permission granted. Face detection with expression analysis ready!");

        // Enable face detection
        if (faceDetectionManager != null) {
            faceDetectionManager.setDetectionEnabled(true);
        }

        // Observe camera provider and start camera
        if (cameraXViewModel != null) {
            cameraXViewModel.getProcessCameraProvider().observe(this, cameraProvider -> {
                if (cameraProvider != null && faceDetectionManager != null) {
                    faceDetectionManager.startCamera(cameraProvider);
                    Log.d(TAG, "Camera started for face detection");
                }
            });
        }
    }

    private void testYoloClassifier() {
        if (yoloClassifier == null) {
            return;
        }

        try {
            // Create a test bitmap
            Bitmap testBitmap = Bitmap.createBitmap(416, 416, Bitmap.Config.ARGB_8888);

            // Test the classifier
            ArrayList<YoloClassifier.Recognition> recognitions = yoloClassifier.recognizeImage(testBitmap);

            String testResult = "YOLO Test Result:\n";
            if (recognitions != null) {
                testResult += "Classifier is working! Found " + recognitions.size() + " detections.\n";
                testResult += "Ready for real camera input.";
            } else {
                testResult += "Classifier returned null - this is normal for empty test image.";
            }

            resultTextView.setText(resultTextView.getText() + "\n\n" + testResult);
            Log.d(TAG, testResult);

        } catch (Exception e) {
            Log.e(TAG, "Error testing YOLO classifier", e);
            resultTextView.setText(resultTextView.getText() + "\n\nError testing classifier: " + e.getMessage());
        }
    }

    // FaceDetectionManager.FaceDetectionCallback implementation
    @Override
    public void onFacesDetected(List<Face> faces, Bitmap cameraBitmap) {
        runOnUiThread(() -> {
            String message = "Detected " + faces.size() + " face(s)";
            if (faces.size() > 0) {
                message += "\nExpression analysis active on closest face";
                message += "\nSmart Greeting active!";
            }
            resultTextView.setText(message);

            // Send faces to Smart Greeting Manager for greeting logic
            if (smartGreetingManager != null && !faces.isEmpty()) {
                smartGreetingManager.processFaces(faces);
                Log.d(TAG, "Sent " + faces.size() + " faces to SmartGreetingManager");
            }
        });
    }

    @Override
    public void onNoFacesDetected() {
        runOnUiThread(() -> {
            resultTextView.setText("No faces detected\nSmart Greeting standby...");

            // Notify Smart Greeting Manager that no faces are detected
            if (smartGreetingManager != null) {
                smartGreetingManager.processFaces(new ArrayList<>());
            }
        });
    }

    @Override
    public void onDetectionError(Exception error) {
        runOnUiThread(() -> {
            resultTextView.setText("Detection error: " + error.getMessage());
            Log.e(TAG, "Face detection error", error);
        });
    }

    @Override
    public void onCameraFrame(Bitmap cameraBitmap) {
        // Camera frame callback - can be used for additional processing
    }

    /**
     * Toggle full-screen mode
     */
    private void toggleFullScreenMode() {
        isFullScreenMode = !isFullScreenMode;

        if (isFullScreenMode) {
            enterFullScreenMode();
        } else {
            exitFullScreenMode();
        }
    }

    /**
     * Enter full-screen mode - hide UI elements except camera and overlays
     * Supports both portrait and landscape orientations
     */
    private void enterFullScreenMode() {
        // Enable immersive full-screen mode for both orientations
        View decorView = getWindow().getDecorView();
        int uiOptions = View.SYSTEM_UI_FLAG_FULLSCREEN
                | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN;

        decorView.setSystemUiVisibility(uiOptions);

        // Hide action bar if present
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }

        // Hide UI elements
        if (titleBar != null) {
            titleBar.setVisibility(View.GONE);
        }
        if (bottomPanel != null) {
            bottomPanel.setVisibility(View.GONE);
        }
        if (resultTextView != null) {
            resultTextView.setVisibility(View.GONE);
        }

        // Show exit button - position it appropriately for current orientation
        if (exitFullScreenButton != null) {
            exitFullScreenButton.setVisibility(View.VISIBLE);
            adjustExitButtonForOrientation();
        }

        // Keep face detection and overlays active
        Log.d(TAG, "Entered full-screen mode for " + getCurrentOrientationString());
    }

    /**
     * Exit full-screen mode - restore UI elements
     * Works in both portrait and landscape orientations
     */
    private void exitFullScreenMode() {
        isFullScreenMode = false;

        // Restore system UI - clear immersive flags
        View decorView = getWindow().getDecorView();
        decorView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_VISIBLE);

        // Clear window flags
        getWindow().clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);

        // Show action bar if present
        if (getSupportActionBar() != null) {
            getSupportActionBar().show();
        }

        // Show UI elements
        if (titleBar != null) {
            titleBar.setVisibility(View.VISIBLE);
        }
        if (bottomPanel != null) {
            bottomPanel.setVisibility(View.VISIBLE);
        }
        if (resultTextView != null) {
            resultTextView.setVisibility(View.VISIBLE);
        }

        // Hide exit button
        if (exitFullScreenButton != null) {
            exitFullScreenButton.setVisibility(View.GONE);
        }

        Log.d(TAG, "Exited full-screen mode from " + getCurrentOrientationString());
    }

    /**
     * Adjust exit button position based on current orientation
     * Ensures button remains accessible in both portrait and landscape modes
     */
    private void adjustExitButtonForOrientation() {
        if (exitFullScreenButton == null) return;

        int orientation = getResources().getConfiguration().orientation;

        // Get layout parameters
        RelativeLayout.LayoutParams params =
            (RelativeLayout.LayoutParams) exitFullScreenButton.getLayoutParams();

        // Clear previous rules
        params.removeRule(RelativeLayout.ALIGN_PARENT_TOP);
        params.removeRule(RelativeLayout.ALIGN_PARENT_END);
        params.removeRule(RelativeLayout.ALIGN_PARENT_START);
        params.removeRule(RelativeLayout.ALIGN_PARENT_BOTTOM);

        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            // In landscape, position button in top-right with larger margins for easier access
            params.addRule(RelativeLayout.ALIGN_PARENT_TOP);
            params.addRule(RelativeLayout.ALIGN_PARENT_END);
            params.setMargins(0, 40, 40, 0); // Larger margins for landscape

            // Make button slightly larger in landscape for easier touch
            params.width = (int) (60 * getResources().getDisplayMetrics().density);
            params.height = (int) (60 * getResources().getDisplayMetrics().density);
        } else {
            // In portrait, standard top-right position
            params.addRule(RelativeLayout.ALIGN_PARENT_TOP);
            params.addRule(RelativeLayout.ALIGN_PARENT_END);
            params.setMargins(0, 30, 30, 0); // Standard margins for portrait

            // Standard button size in portrait
            params.width = (int) (50 * getResources().getDisplayMetrics().density);
            params.height = (int) (50 * getResources().getDisplayMetrics().density);
        }

        exitFullScreenButton.setLayoutParams(params);

        Log.d(TAG, "Adjusted exit button for " + getCurrentOrientationString() + " orientation");
    }

    /**
     * Get current orientation as string for logging
     */
    private String getCurrentOrientationString() {
        int orientation = getResources().getConfiguration().orientation;
        switch (orientation) {
            case Configuration.ORIENTATION_LANDSCAPE:
                return "landscape";
            case Configuration.ORIENTATION_PORTRAIT:
                return "portrait";
            default:
                return "undefined";
        }
    }

    /**
     * Handle configuration changes (orientation changes)
     */
    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);

        Log.d(TAG, "Orientation changed to: " + getCurrentOrientationString());

        // Update face detection manager for new orientation
        if (faceDetectionManager != null) {
            faceDetectionManager.handleOrientationChange();
        }

        // If in full-screen mode, adjust UI for new orientation
        if (isFullScreenMode) {
            // Re-apply immersive mode for new orientation
            View decorView = getWindow().getDecorView();
            int uiOptions = View.SYSTEM_UI_FLAG_FULLSCREEN
                    | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                    | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN;

            decorView.setSystemUiVisibility(uiOptions);

            // Adjust exit button position for new orientation
            adjustExitButtonForOrientation();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // Cleanup vision system
        if (faceDetectionManager != null) {
            faceDetectionManager.cleanup();
        }

        if (yoloClassifier != null) {
            yoloClassifier.close();
        }
    }
}