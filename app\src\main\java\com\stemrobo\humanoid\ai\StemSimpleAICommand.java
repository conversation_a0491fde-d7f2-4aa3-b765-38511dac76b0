package com.stemrobo.humanoid.ai;

/**
 * Stem-Simple AI Command structure
 * Represents executable commands parsed from AI responses
 */
public class StemSimpleAICommand {
    private String type;
    private String action;
    private int duration; // Duration in milliseconds (for movement commands)
    private String parameter; // Additional parameter if needed
    
    public StemSimpleAICommand() {
        // Default constructor for JSON parsing
    }
    
    public StemSimpleAICommand(String type, String action, int duration) {
        this.type = type;
        this.action = action;
        this.duration = duration;
    }
    
    public StemSimpleAICommand(String type, String action, int duration, String parameter) {
        this.type = type;
        this.action = action;
        this.duration = duration;
        this.parameter = parameter;
    }
    
    // Getters
    public String getType() {
        return type;
    }
    
    public String getAction() {
        return action;
    }
    
    public int getDuration() {
        return duration;
    }
    
    public String getParameter() {
        return parameter;
    }
    
    // Setters
    public void setType(String type) {
        this.type = type;
    }
    
    public void setAction(String action) {
        this.action = action;
    }
    
    public void setDuration(int duration) {
        this.duration = duration;
    }
    
    public void setParameter(String parameter) {
        this.parameter = parameter;
    }
    
    // Utility methods
    public boolean isMovementCommand() {
        return "movement".equalsIgnoreCase(type);
    }
    
    public boolean isServoCommand() {
        return "servo".equalsIgnoreCase(type);
    }
    
    public boolean isSensorCommand() {
        return "sensor".equalsIgnoreCase(type);
    }
    
    public boolean isGreetingCommand() {
        return "greeting".equalsIgnoreCase(type);
    }
    
    public boolean hasParameter() {
        return parameter != null && !parameter.trim().isEmpty();
    }
    
    public boolean hasDuration() {
        return duration > 0;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("StemSimpleAICommand{");
        sb.append("type='").append(type).append('\'');
        sb.append(", action='").append(action).append('\'');
        if (duration > 0) {
            sb.append(", duration=").append(duration);
        }
        if (parameter != null) {
            sb.append(", parameter='").append(parameter).append('\'');
        }
        sb.append('}');
        return sb.toString();
    }
}
