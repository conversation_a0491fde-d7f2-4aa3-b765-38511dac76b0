package com.stemrobo.humanoid.ai;

/**
 * Stem-Simple AI Response structure
 * Contains both natural language response and executable commands
 */
public class StemSimpleAIResponse {
    private String response;
    private StemSimpleAICommand[] commands;
    private String presetName;
    
    public StemSimpleAIResponse(String response, StemSimpleAICommand[] commands, String presetName) {
        this.response = response;
        this.commands = commands;
        this.presetName = presetName;
    }
    
    // Getters
    public String getResponse() {
        return response;
    }
    
    public StemSimpleAICommand[] getCommands() {
        return commands;
    }
    
    public String getPresetName() {
        return presetName;
    }
    
    // Setters
    public void setResponse(String response) {
        this.response = response;
    }
    
    public void setCommands(StemSimpleAICommand[] commands) {
        this.commands = commands;
    }
    
    public void setPresetName(String presetName) {
        this.presetName = presetName;
    }
    
    // Utility methods
    public boolean hasCommands() {
        return commands != null && commands.length > 0;
    }
    
    public boolean isPresetCreation() {
        return presetName != null && !presetName.trim().isEmpty();
    }
    
    public int getCommandCount() {
        return commands != null ? commands.length : 0;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("StemSimpleAIResponse{");
        sb.append("response='").append(response).append('\'');
        sb.append(", commandCount=").append(getCommandCount());
        if (presetName != null) {
            sb.append(", presetName='").append(presetName).append('\'');
        }
        sb.append('}');
        return sb.toString();
    }
}
