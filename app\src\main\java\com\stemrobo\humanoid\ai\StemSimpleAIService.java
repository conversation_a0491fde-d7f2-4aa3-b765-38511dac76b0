package com.stemrobo.humanoid.ai;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

/**
 * Stem-Simple AI Service - Direct voice to AI with JSON command parsing
 * Mimics the stem-simple app's AI interaction pattern
 */
public class StemSimpleAIService {
    private static final String TAG = "StemSimpleAIService";

    private final Context context;
    private final Gson gson;
    private GeminiAIService geminiService;
    private StemSimpleUnifiedAIService unifiedAIService;
    
    public StemSimpleAIService(Context context) {
        this.context = context;
        this.gson = new Gson();
        this.geminiService = new GeminiAIService(context);
    }
    
    /**
     * Process user voice input directly to AI with structured JSON response
     */
    public void processVoiceInput(String userInput, StemSimpleAICallback callback) {
        String enhancedPrompt = createStemSimplePrompt(userInput);
        
        geminiService.getAIResponse(enhancedPrompt, "en", new GeminiAIService.AIResponseCallback() {
            @Override
            public void onSuccess(String response) {
                StemSimpleAIResponse structuredResponse = parseStructuredResponse(response);
                if (structuredResponse != null) {
                    callback.onSuccess(structuredResponse);
                } else {
                    // Fallback to simple response
                    callback.onSuccess(new StemSimpleAIResponse(response, null, null));
                }
            }
            
            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    /**
     * Create stem-simple style prompt for structured JSON responses
     */
    private String createStemSimplePrompt(String userInput) {
        return "You are a humanoid robot assistant. The user said: \"" + userInput + "\"\n\n" +
               
               "IMPORTANT: Always respond with a JSON structure like this:\n" +
               "{\n" +
               "  \"commands\": [\n" +
               "    {\"type\": \"movement\", \"action\": \"forward\", \"duration\": 3000},\n" +
               "    {\"type\": \"servo\", \"action\": \"wave\"},\n" +
               "    {\"type\": \"sensor\", \"action\": \"get_distance\"}\n" +
               "  ],\n" +
               "  \"response\": \"I'm moving forward for 3 seconds and waving!\",\n" +
               "  \"preset_name\": \"greeting_sequence\"\n" +
               "}\n\n" +
               
               "Command types:\n" +
               "- movement: forward, backward, left, right, stop (with optional duration in ms)\n" +
               "- servo: wave, point, rest, handshake\n" +
               "- sensor: get_distance\n" +
               "- greeting: trigger_greeting\n\n" +
               
               "Rules:\n" +
               "1. Always include a 'response' field with natural language\n" +
               "2. Include 'commands' array only if robot actions are needed\n" +
               "3. Include 'preset_name' only if creating a new preset\n" +
               "4. Keep responses concise and friendly\n" +
               "5. Respond in the same language as the user input\n\n" +
               
               "User input: " + userInput;
    }
    
    /**
     * Parse structured JSON response from AI
     */
    private StemSimpleAIResponse parseStructuredResponse(String aiResponse) {
        try {
            String jsonStr = extractJsonFromResponse(aiResponse);
            if (jsonStr != null) {
                JsonObject jsonResponse = JsonParser.parseString(jsonStr).getAsJsonObject();
                
                // Extract commands
                StemSimpleAICommand[] commands = null;
                if (jsonResponse.has("commands")) {
                    commands = gson.fromJson(jsonResponse.getAsJsonArray("commands"), StemSimpleAICommand[].class);
                }
                
                // Extract response text
                String responseText = aiResponse; // Default to full response
                if (jsonResponse.has("response")) {
                    responseText = jsonResponse.get("response").getAsString();
                }
                
                // Extract preset name
                String presetName = null;
                if (jsonResponse.has("preset_name")) {
                    presetName = jsonResponse.get("preset_name").getAsString();
                }
                
                return new StemSimpleAIResponse(responseText, commands, presetName);
            }
            
            // If no JSON found, return simple response
            return new StemSimpleAIResponse(aiResponse, null, null);
            
        } catch (Exception e) {
            Log.e(TAG, "Error parsing structured response", e);
            return new StemSimpleAIResponse(aiResponse, null, null);
        }
    }
    
    /**
     * Extract JSON from AI response text
     */
    private String extractJsonFromResponse(String response) {
        try {
            int jsonStart = response.indexOf("{");
            int jsonEnd = response.lastIndexOf("}");
            
            if (jsonStart != -1 && jsonEnd != -1 && jsonEnd > jsonStart) {
                return response.substring(jsonStart, jsonEnd + 1);
            }
            
            return null;
        } catch (Exception e) {
            Log.e(TAG, "Error extracting JSON", e);
            return null;
        }
    }
    
    /**
     * Callback interface for stem-simple AI responses
     */
    public interface StemSimpleAICallback {
        void onSuccess(StemSimpleAIResponse response);
        void onError(String error);
    }
}
