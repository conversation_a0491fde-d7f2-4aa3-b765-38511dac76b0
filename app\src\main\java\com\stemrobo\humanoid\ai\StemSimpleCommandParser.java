package com.stemrobo.humanoid.ai;

import android.content.Context;
import android.util.Log;

import com.stemrobo.humanoid.communication.ESP32CommunicationManager;

/**
 * Command Parser for Stem-Simple AI Commands
 * Executes robot commands from AI responses
 */
public class StemSimpleCommandParser {
    private static final String TAG = "StemSimpleCommandParser";
    
    private final Context context;
    private ESP32CommunicationManager communicationManager;
    
    public StemSimpleCommandParser(Context context) {
        this.context = context;
    }
    
    public void setCommunicationManager(ESP32CommunicationManager communicationManager) {
        this.communicationManager = communicationManager;
    }
    
    /**
     * Execute array of AI commands
     */
    public void executeCommands(StemSimpleAICommand[] commands) {
        if (commands == null || commands.length == 0) {
            Log.d(TAG, "No commands to execute");
            return;
        }
        
        Log.d(TAG, "Executing " + commands.length + " commands");
        
        for (StemSimpleAICommand command : commands) {
            try {
                executeCommand(command);
                
                // Add small delay between commands for smooth execution
                if (command.hasDuration()) {
                    Thread.sleep(100); // 100ms delay
                }
            } catch (Exception e) {
                Log.e(TAG, "Error executing command: " + command, e);
            }
        }
    }
    
    /**
     * Execute single AI command
     */
    private void executeCommand(StemSimpleAICommand command) {
        if (command == null) {
            return;
        }
        
        String type = command.getType();
        if (type == null) {
            Log.w(TAG, "Command type is null");
            return;
        }
        
        switch (type.toLowerCase()) {
            case "movement":
                executeMovementCommand(command);
                break;
            case "servo":
                executeServoCommand(command);
                break;
            case "sensor":
                executeSensorCommand(command);
                break;
            case "greeting":
                executeGreetingCommand(command);
                break;
            default:
                Log.w(TAG, "Unknown command type: " + type);
        }
    }
    
    /**
     * Execute movement commands
     */
    private void executeMovementCommand(StemSimpleAICommand command) {
        if (communicationManager == null) {
            Log.w(TAG, "Communication manager not available for movement command");
            return;
        }
        
        String action = command.getAction().toLowerCase();
        int duration = command.getDuration();
        
        switch (action) {
            case "forward":
                communicationManager.sendMotorCommand("F");
                // Schedule stop after duration
                if (duration > 0) {
                    new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() ->
                        communicationManager.sendMotorCommand("S"), duration);
                }
                break;
            case "backward":
                communicationManager.sendMotorCommand("B");
                if (duration > 0) {
                    new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() ->
                        communicationManager.sendMotorCommand("S"), duration);
                }
                break;
            case "left":
                communicationManager.sendMotorCommand("L");
                if (duration > 0) {
                    new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() ->
                        communicationManager.sendMotorCommand("S"), duration);
                }
                break;
            case "right":
                communicationManager.sendMotorCommand("R");
                if (duration > 0) {
                    new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() ->
                        communicationManager.sendMotorCommand("S"), duration);
                }
                break;
            case "stop":
                communicationManager.sendMotorCommand("S");
                break;
            default:
                Log.w(TAG, "Unknown movement action: " + action);
        }
        
        Log.d(TAG, "Movement command executed: " + action + " for " + duration + "ms");
    }
    
    /**
     * Execute servo commands
     */
    private void executeServoCommand(StemSimpleAICommand command) {
        if (communicationManager == null) {
            Log.w(TAG, "Communication manager not available for servo command");
            return;
        }
        
        String action = command.getAction().toLowerCase();
        
        switch (action) {
            case "wave":
                communicationManager.sendServoCommand("wave");
                break;
            case "point":
                communicationManager.sendServoCommand("point");
                break;
            case "rest":
                communicationManager.sendServoCommand("rest");
                break;
            case "handshake":
                communicationManager.sendServoCommand("handshake");
                break;
            default:
                Log.w(TAG, "Unknown servo action: " + action);
        }
        
        Log.d(TAG, "Servo command executed: " + action);
    }
    
    /**
     * Execute sensor commands
     */
    private void executeSensorCommand(StemSimpleAICommand command) {
        if (communicationManager == null) {
            Log.w(TAG, "Communication manager not available for sensor command");
            return;
        }
        
        String action = command.getAction().toLowerCase();
        
        switch (action) {
            case "get_distance":
                communicationManager.sendSensorCommand("GET_DISTANCE", null);
                break;
            default:
                Log.w(TAG, "Unknown sensor action: " + action);
        }
        
        Log.d(TAG, "Sensor command executed: " + action);
    }
    
    /**
     * Execute greeting commands
     */
    private void executeGreetingCommand(StemSimpleAICommand command) {
        String action = command.getAction().toLowerCase();
        
        switch (action) {
            case "trigger_greeting":
                // Trigger smart greeting sequence
                if (communicationManager != null) {
                    communicationManager.sendServoCommand("wave");
                }
                break;
            default:
                Log.w(TAG, "Unknown greeting action: " + action);
        }
        
        Log.d(TAG, "Greeting command executed: " + action);
    }
}
