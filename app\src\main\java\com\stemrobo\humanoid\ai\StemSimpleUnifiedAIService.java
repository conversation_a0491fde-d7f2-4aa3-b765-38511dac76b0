package com.stemrobo.humanoid.ai;

import android.content.Context;
import android.util.Log;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import okhttp3.*;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * Unified AI Service for STEM Simple Robot
 * Provides structured AI responses that include both commands and natural language
 * Supports preset creation and execution through natural language
 */
public class StemSimpleUnifiedAIService {
    private static final String TAG = "StemSimpleUnifiedAIService";
    
    // Gemini API Configuration
    private static final String GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent";
    private static final String API_KEY = "AIzaSyDP2FAqAXkaoVcuQekdAmXSV8FI5A3zYWY"; // Replace with actual API key
    
    private final Context context;
    private final OkHttpClient httpClient;
    private final Gson gson;
    
    public interface AIResponseCallback {
        void onSuccess(StemSimpleAIResponse response);
        void onError(String error);
    }
    
    public StemSimpleUnifiedAIService(Context context) {
        this.context = context;
        this.gson = new Gson();
        this.httpClient = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build();
        
        Log.d(TAG, "Unified AI Service initialized");
    }
    
    /**
     * Process user input and generate structured AI response
     */
    public void processUserInput(String userInput, AIResponseCallback callback) {
        // Create enhanced prompt for structured responses
        String enhancedPrompt = createStructuredPrompt(userInput);
        
        // Call Gemini API
        callGeminiAPI(enhancedPrompt, new AIResponseCallback() {
            @Override
            public void onSuccess(StemSimpleAIResponse response) {
                // Parse and validate the structured response
                StemSimpleAIResponse structuredResponse = parseStructuredResponse(response.getResponse());
                if (structuredResponse != null) {
                    callback.onSuccess(structuredResponse);
                } else {
                    // Fallback to simple response
                    callback.onSuccess(response);
                }
            }
            
            @Override
            public void onError(String error) {
                callback.onError(error);
            }
        });
    }
    
    private String createStructuredPrompt(String userInput) {
        return "You are Guruji, an AI assistant for a STEM robotics system. " +
               "The robot has these capabilities:\n" +
               "- Movement: forward, backward, left, right, stop\n" +
               "- Servo actions: wave, point, rest, handshake\n" +
               "- Sensors: ultrasonic distance measurement\n" +
               "- Smart greeting: automatic greeting when faces detected\n\n" +
               
               "IMPORTANT: Always respond with a JSON structure like this:\n" +
               "{\n" +
               "  \"commands\": [\n" +
               "    {\"type\": \"movement\", \"action\": \"forward\", \"duration\": 3000},\n" +
               "    {\"type\": \"servo\", \"action\": \"wave\"},\n" +
               "    {\"type\": \"sensor\", \"action\": \"get_distance\"}\n" +
               "  ],\n" +
               "  \"response\": \"I'm moving forward for 3 seconds and waving!\",\n" +
               "  \"preset_name\": \"greeting_sequence\"\n" +
               "}\n\n" +
               
               "Command types:\n" +
               "- movement: forward, backward, left, right, stop (with optional duration in ms)\n" +
               "- servo: wave, point, rest, handshake\n" +
               "- sensor: get_distance\n" +
               "- greeting: trigger_greeting\n\n" +
               
               "If the user wants to create a preset, include preset_name.\n" +
               "If no commands are needed, use empty commands array [].\n" +
               "Always include a friendly response message.\n\n" +
               
               "User input: " + userInput;
    }
    
    private void callGeminiAPI(String prompt, AIResponseCallback callback) {
        try {
            // Create request body
            JsonObject requestBody = new JsonObject();
            JsonObject contents = new JsonObject();
            JsonObject parts = new JsonObject();
            parts.addProperty("text", prompt);
            
            contents.add("parts", gson.toJsonTree(new JsonObject[]{gson.fromJson(parts.toString(), JsonObject.class)}));
            requestBody.add("contents", gson.toJsonTree(new JsonObject[]{gson.fromJson(contents.toString(), JsonObject.class)}));
            
            // Create HTTP request
            RequestBody body = RequestBody.create(
                requestBody.toString(),
                MediaType.parse("application/json")
            );
            
            Request request = new Request.Builder()
                .url(GEMINI_API_URL + "?key=" + API_KEY)
                .post(body)
                .addHeader("Content-Type", "application/json")
                .build();
            
            Log.d(TAG, "Sending request to Gemini API");
            
            // Execute request asynchronously
            httpClient.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "Gemini API call failed", e);
                    callback.onError("AI service unavailable: " + e.getMessage());
                }
                
                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        if (response.isSuccessful() && response.body() != null) {
                            String responseBody = response.body().string();
                            Log.d(TAG, "Gemini API response received");
                            
                            // Parse Gemini response
                            String aiResponse = parseGeminiResponse(responseBody);
                            if (aiResponse != null) {
                                StemSimpleAIResponse result = new StemSimpleAIResponse(aiResponse, null, null);
                                callback.onSuccess(result);
                            } else {
                                callback.onError("Failed to parse AI response");
                            }
                        } else {
                            Log.e(TAG, "Gemini API error: " + response.code());
                            callback.onError("AI service error: HTTP " + response.code());
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error processing Gemini response", e);
                        callback.onError("Error processing AI response: " + e.getMessage());
                    }
                }
            });
            
        } catch (Exception e) {
            Log.e(TAG, "Error calling Gemini API", e);
            callback.onError("AI service error: " + e.getMessage());
        }
    }
    
    private String parseGeminiResponse(String responseBody) {
        try {
            JsonObject jsonResponse = JsonParser.parseString(responseBody).getAsJsonObject();
            
            if (jsonResponse.has("candidates") && jsonResponse.getAsJsonArray("candidates").size() > 0) {
                JsonObject candidate = jsonResponse.getAsJsonArray("candidates").get(0).getAsJsonObject();
                if (candidate.has("content")) {
                    JsonObject content = candidate.getAsJsonObject("content");
                    if (content.has("parts") && content.getAsJsonArray("parts").size() > 0) {
                        JsonObject part = content.getAsJsonArray("parts").get(0).getAsJsonObject();
                        if (part.has("text")) {
                            return part.get("text").getAsString();
                        }
                    }
                }
            }
            
            Log.e(TAG, "Unexpected Gemini response format: " + responseBody);
            return null;
            
        } catch (Exception e) {
            Log.e(TAG, "Error parsing Gemini response", e);
            return null;
        }
    }
    
    private StemSimpleAIResponse parseStructuredResponse(String aiResponse) {
        try {
            // Try to extract JSON from the response
            String jsonStr = extractJsonFromResponse(aiResponse);
            if (jsonStr != null) {
                JsonObject jsonResponse = JsonParser.parseString(jsonStr).getAsJsonObject();
                
                // Extract commands
                StemSimpleAICommand[] commands = null;
                if (jsonResponse.has("commands")) {
                    commands = gson.fromJson(jsonResponse.getAsJsonArray("commands"), StemSimpleAICommand[].class);
                }
                
                // Extract response text
                String responseText = aiResponse; // Default to full response
                if (jsonResponse.has("response")) {
                    responseText = jsonResponse.get("response").getAsString();
                }
                
                // Extract preset name
                String presetName = null;
                if (jsonResponse.has("preset_name")) {
                    presetName = jsonResponse.get("preset_name").getAsString();
                }
                
                return new StemSimpleAIResponse(responseText, commands, presetName);
            }
            
            // If no JSON found, return simple response
            return new StemSimpleAIResponse(aiResponse, null, null);
            
        } catch (Exception e) {
            Log.e(TAG, "Error parsing structured response", e);
            return new StemSimpleAIResponse(aiResponse, null, null);
        }
    }
    
    private String extractJsonFromResponse(String response) {
        try {
            // Look for JSON object in the response
            int startIndex = response.indexOf("{");
            int endIndex = response.lastIndexOf("}");
            
            if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
                return response.substring(startIndex, endIndex + 1);
            }
            
            return null;
        } catch (Exception e) {
            Log.e(TAG, "Error extracting JSON from response", e);
            return null;
        }
    }
    
    /**
     * Generate a simple fallback response for offline mode
     */
    public StemSimpleAIResponse generateFallbackResponse(String userInput) {
        String lowerInput = userInput.toLowerCase();
        
        // Simple command recognition
        if (lowerInput.contains("forward") || lowerInput.contains("move forward")) {
            StemSimpleAICommand[] commands = {new StemSimpleAICommand("movement", "forward", 3000)};
            return new StemSimpleAIResponse("Moving forward for 3 seconds!", commands, null);
        } else if (lowerInput.contains("backward") || lowerInput.contains("move backward")) {
            StemSimpleAICommand[] commands = {new StemSimpleAICommand("movement", "backward", 3000)};
            return new StemSimpleAIResponse("Moving backward for 3 seconds!", commands, null);
        } else if (lowerInput.contains("left") || lowerInput.contains("turn left")) {
            StemSimpleAICommand[] commands = {new StemSimpleAICommand("movement", "left", 2000)};
            return new StemSimpleAIResponse("Turning left for 2 seconds!", commands, null);
        } else if (lowerInput.contains("right") || lowerInput.contains("turn right")) {
            StemSimpleAICommand[] commands = {new StemSimpleAICommand("movement", "right", 2000)};
            return new StemSimpleAIResponse("Turning right for 2 seconds!", commands, null);
        } else if (lowerInput.contains("stop")) {
            StemSimpleAICommand[] commands = {new StemSimpleAICommand("movement", "stop", 0)};
            return new StemSimpleAIResponse("Stopping all movement!", commands, null);
        } else if (lowerInput.contains("wave")) {
            StemSimpleAICommand[] commands = {new StemSimpleAICommand("servo", "wave", 0)};
            return new StemSimpleAIResponse("Waving hello!", commands, null);
        } else if (lowerInput.contains("point")) {
            StemSimpleAICommand[] commands = {new StemSimpleAICommand("servo", "point", 0)};
            return new StemSimpleAIResponse("Pointing ahead!", commands, null);
        } else if (lowerInput.contains("rest")) {
            StemSimpleAICommand[] commands = {new StemSimpleAICommand("servo", "rest", 0)};
            return new StemSimpleAIResponse("Moving to rest position!", commands, null);
        } else if (lowerInput.contains("distance") || lowerInput.contains("how far")) {
            StemSimpleAICommand[] commands = {new StemSimpleAICommand("sensor", "get_distance", 0)};
            return new StemSimpleAIResponse("Checking distance with ultrasonic sensor!", commands, null);
        } else if (lowerInput.contains("hello") || lowerInput.contains("greet")) {
            StemSimpleAICommand[] commands = {new StemSimpleAICommand("greeting", "trigger_greeting", 0)};
            return new StemSimpleAIResponse("Performing greeting sequence!", commands, null);
        } else {
            return new StemSimpleAIResponse("I understand you said: " + userInput + ". I'm ready to help with robot commands!", null, null);
        }
    }
    
    public void cleanup() {
        if (httpClient != null) {
            httpClient.dispatcher().executorService().shutdown();
            httpClient.connectionPool().evictAll();
        }
        Log.d(TAG, "Unified AI Service cleaned up");
    }
}
