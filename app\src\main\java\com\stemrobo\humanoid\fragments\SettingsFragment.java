package com.stemrobo.humanoid.fragments;

import android.app.AlertDialog;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.preference.PreferenceManager;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ScrollView;
import android.widget.SeekBar;
import android.widget.Spinner;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.preference.PreferenceManager;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ScrollView;
import android.widget.SeekBar;
import android.widget.Spinner;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import android.content.SharedPreferences;
import com.stemrobo.humanoid.activities.VoiceCommandTestActivity;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.preference.PreferenceManager;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ScrollView;
import android.widget.SeekBar;
import android.widget.Spinner;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.stemrobo.humanoid.communication.ESP32CommunicationManager;

import java.util.Map;
import android.preference.PreferenceManager;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.SeekBar;
import android.widget.Spinner;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.stemrobo.humanoid.R;

import com.stemrobo.humanoid.language.LanguageManager;
import com.stemrobo.humanoid.ai.GeminiModelConfig;
import com.stemrobo.humanoid.ai.GeminiAIService;

/**
 * Settings Fragment for configuring robot parameters
 * Includes AI, voice, ESP32, and system settings
 */
public class SettingsFragment extends Fragment {
    private static final String TAG = "SettingsFragment";
    
    // Settings Keys
    public static final String PREF_MAX_AI_OUTPUT = "max_ai_output";
    public static final String PREF_SPEECH_SPEED = "speech_speed";
    public static final String PREF_MIC_SENSITIVITY = "mic_sensitivity";
    public static final String PREF_MIC_DURATION = "mic_duration";
    public static final String PREF_MOTOR_CONTROLLER_IP = "motor_controller_ip";
    public static final String PREF_SERVO_CONTROLLER_IP = "servo_controller_ip";
    public static final String PREF_SENSOR_CONTROLLER_IP = "sensor_controller_ip";
    public static final String PREF_ESP32_PORT = "esp32_port";
    public static final String PREF_WAKE_WORD_ENABLED = "wake_word_enabled";
    public static final String PREF_AUTO_LISTEN = "auto_listen";
    public static final String PREF_VOICE_FEEDBACK = "voice_feedback";
    public static final String PREF_DEBUG_MODE = "debug_mode";
    public static final String PREF_VOICE_FORWARD_DURATION = "voice_forward_duration";
    public static final String PREF_VOICE_SIDE_DURATION = "voice_side_duration";


    public static final String PREF_ULTRASONIC_SENSOR_ENABLED = "ultrasonic_sensor_enabled";
    public static final String PREF_ULTRASONIC_FACE_INTEGRATION_ENABLED = "ultrasonic_face_integration_enabled";
    public static final String PREF_BACKGROUND_CAMERA_ENABLED = "background_camera_enabled";
    public static final String PREF_ULTRASONIC_DETECTION_DISTANCE = "ultrasonic_detection_distance";
    public static final String PREF_VOICE_GENDER = "voice_gender";
    public static final String PREF_CONTINUOUS_LISTENING = "continuous_listening";



    // Smart Greeting Settings
    public static final String PREF_FACE_DETECTION_DURATION = "face_detection_duration";
    public static final String PREF_GREETING_DISTANCE_THRESHOLD = "greeting_distance_threshold";
    public static final String PREF_GREETING_COOLDOWN = "greeting_cooldown";
    public static final String PREF_HANDSHAKE_DURATION = "handshake_duration";
    public static final String PREF_SMART_GREETING_DISABLED = "smart_greeting_disabled";

    // Default Values
    public static final int DEFAULT_MAX_AI_OUTPUT = 150;
    public static final float DEFAULT_SPEECH_SPEED = 1.0f;
    public static final int DEFAULT_MIC_SENSITIVITY = 50;
    public static final int DEFAULT_MIC_DURATION = 4000;
    public static final String DEFAULT_MOTOR_CONTROLLER_IP = "***************";
    public static final String DEFAULT_SERVO_CONTROLLER_IP = "***************";
    public static final String DEFAULT_SENSOR_CONTROLLER_IP = "***************";
    public static final int DEFAULT_ESP32_PORT = 80;
    public static final boolean DEFAULT_ULTRASONIC_FACE_INTEGRATION_ENABLED = false;
    public static final int DEFAULT_ULTRASONIC_DETECTION_DISTANCE = 10; // 10cm default
    public static final String DEFAULT_VOICE_GENDER = "female"; // Default to female voice



    // Smart Greeting Default Values
    public static final int DEFAULT_FACE_DETECTION_DURATION = 2000; // 2 seconds in milliseconds
    public static final int DEFAULT_GREETING_DISTANCE_THRESHOLD = 30; // 30cm
    public static final int DEFAULT_GREETING_COOLDOWN = 15000; // 15 seconds in milliseconds
    public static final int DEFAULT_HANDSHAKE_DURATION = 5000; // 5 seconds in milliseconds
    public static final boolean DEFAULT_SMART_GREETING_DISABLED = false;
    public static final boolean DEFAULT_CONTINUOUS_LISTENING = false;
    
    // UI Components
    private SeekBar maxAiOutputSeekBar;
    private TextView maxAiOutputValue;
    private SeekBar aiContextPairsSeekBar;
    private TextView aiContextPairsValue;
    private SeekBar speechSpeedSeekBar;
    private TextView speechSpeedValue;
    private Spinner voiceGenderSpinner;
    private SeekBar micSensitivitySeekBar;
    private TextView micSensitivityValue;
    private SeekBar micDurationSeekBar;
    private TextView micDurationValue;
    private TextView connectionModeIndicator;
    private TextView connectionIpDisplay;
    
    // Gemini Model Configuration UI Components
    private Spinner geminiModelSpinner;
    private TextView geminiModelStatus;
    private EditText model1NameInput;
    private EditText model1ApiKeyInput;
    private EditText model1EndpointInput;
    private EditText model2NameInput;
    private EditText model2ApiKeyInput;
    private EditText model2EndpointInput;
    private Button testModel1Button;
    private Button testModel2Button;
    private GeminiModelConfig modelConfig;
    private GeminiAIService geminiAIService;

    // Voice Command Duration Settings
    private SeekBar voiceForwardDurationSeekBar;
    private TextView voiceForwardDurationValue;
    private SeekBar voiceSideDurationSeekBar;
    private TextView voiceSideDurationValue;


    private Button testVoiceCommandsButton;

    // AI Context Settings
    private SeekBar contextCachingSeekBar;
    private TextView contextCachingValue;


    private Button btnToggleCommunicationMode;
    private Button btnScanUsb;
    private Button btnUsbDebug;
    private Button btnFixUsb;
    private Spinner baudRateSpinner;
    private EditText portNumberInput;
    private EditText motorControllerIpInput;
    private EditText servoControllerIpInput;
    private EditText sensorControllerIpInput;
    private EditText esp32PortInput;
    private Button testConnectionButton;
    private Button testWifiButton;
    private Button forceWifiButton;
    private Switch wakeWordSwitch;
    private Switch autoListenSwitch;
    private Switch voiceFeedbackSwitch;
    private Switch debugModeSwitch;
    private Switch ultrasonicSensorSwitch;
    private Switch ultrasonicFaceIntegrationSwitch;
    private Switch backgroundCameraSwitch;
    private EditText ultrasonicDetectionDistanceInput;
    private Switch continuousListeningSwitch;
    private Switch stemSimpleModeSwitch;
    private TextView stemSimpleModeStatus;

    // Smart Greeting Settings
    private SeekBar faceDetectionDurationSeekBar;
    private TextView faceDetectionDurationValue;
    private SeekBar greetingDistanceSeekBar;
    private TextView greetingDistanceValue;
    private SeekBar greetingCooldownSeekBar;
    private TextView greetingCooldownValue;
    private SeekBar handshakeDurationSeekBar;
    private TextView handshakeDurationValue;
    private Switch disableSmartGreetingSwitch;

    // Walking Animation Settings
    private SeekBar walkingSpeedSeekBar;
    private TextView walkingSpeedValue;
    private SeekBar leftArmRestSeekBar;
    private TextView leftArmRestValue;
    private SeekBar rightArmRestSeekBar;
    private TextView rightArmRestValue;
    private Button testWalkingButton;

    private Button saveButton;
    private Button resetButton;

    // Serial Monitor Components
    private TextView serialMonitorText;
    private ScrollView serialMonitorScroll;
    private EditText serialCommandInput;
    private Button btnStartMonitor;
    private Button btnStopMonitor;
    private Button btnClearMonitor;
    private Button btnSendCommand;
    private Switch switchAutoScroll;
    private Switch switchShowTimestamps;

    // Serial Monitor State
    private boolean isMonitoringActive = false;
    private StringBuilder serialBuffer = new StringBuilder();
    private Handler uiHandler = new Handler(Looper.getMainLooper());
    
    private SharedPreferences sharedPreferences;
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, 
                           @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_settings, container, false);
        
        initializePreferences();
        initializeViews(view);
        loadSettings();
        setupListeners();
        updateConnectionModeDisplay();

        return view;
    }
    
    private void initializePreferences() {
        sharedPreferences = PreferenceManager.getDefaultSharedPreferences(requireContext());
        
        // Initialize Gemini model configuration
        modelConfig = new GeminiModelConfig(requireContext());
        geminiAIService = new GeminiAIService(requireContext());
    }
    
    private void initializeViews(View view) {
        // AI Settings
        maxAiOutputSeekBar = view.findViewById(R.id.max_ai_output_seekbar);
        maxAiOutputValue = view.findViewById(R.id.max_ai_output_value);
        aiContextPairsSeekBar = view.findViewById(R.id.ai_context_pairs_seekbar);
        aiContextPairsValue = view.findViewById(R.id.ai_context_pairs_value);
        
        // Gemini Model Configuration
        geminiModelSpinner = view.findViewById(R.id.gemini_model_spinner);
        geminiModelStatus = view.findViewById(R.id.gemini_model_status);
        model1NameInput = view.findViewById(R.id.model1_name_input);
        model1ApiKeyInput = view.findViewById(R.id.model1_api_key_input);
        model1EndpointInput = view.findViewById(R.id.model1_endpoint_input);
        model2NameInput = view.findViewById(R.id.model2_name_input);
        model2ApiKeyInput = view.findViewById(R.id.model2_api_key_input);
        model2EndpointInput = view.findViewById(R.id.model2_endpoint_input);
        testModel1Button = view.findViewById(R.id.test_model1_button);
        testModel2Button = view.findViewById(R.id.test_model2_button);

        // Voice Settings
        speechSpeedSeekBar = view.findViewById(R.id.speech_speed_seekbar);
        speechSpeedValue = view.findViewById(R.id.speech_speed_value);
        voiceGenderSpinner = view.findViewById(R.id.voice_gender_spinner);
        micSensitivitySeekBar = view.findViewById(R.id.mic_sensitivity_seekbar);
        micSensitivityValue = view.findViewById(R.id.mic_sensitivity_value);
        micDurationSeekBar = view.findViewById(R.id.mic_duration_seekbar);
        micDurationValue = view.findViewById(R.id.mic_duration_value);

        // Voice Command Duration Settings
        voiceForwardDurationSeekBar = view.findViewById(R.id.voice_forward_duration_seekbar);
        voiceForwardDurationValue = view.findViewById(R.id.voice_forward_duration_value);
        voiceSideDurationSeekBar = view.findViewById(R.id.voice_side_duration_seekbar);
        voiceSideDurationValue = view.findViewById(R.id.voice_side_duration_value);



        testVoiceCommandsButton = view.findViewById(R.id.test_voice_commands_button);

        // ESP32 Settings
        connectionModeIndicator = view.findViewById(R.id.connection_mode_indicator);
        connectionIpDisplay = view.findViewById(R.id.connection_ip_display);
        btnToggleCommunicationMode = view.findViewById(R.id.btn_toggle_communication_mode);
        btnScanUsb = view.findViewById(R.id.btn_scan_usb);
        btnUsbDebug = view.findViewById(R.id.btn_usb_debug);
        btnFixUsb = view.findViewById(R.id.btn_fix_usb);
        baudRateSpinner = view.findViewById(R.id.baud_rate_spinner);
        portNumberInput = view.findViewById(R.id.port_number_input);
        motorControllerIpInput = view.findViewById(R.id.motor_controller_ip_input);
        servoControllerIpInput = view.findViewById(R.id.servo_controller_ip_input);
        sensorControllerIpInput = view.findViewById(R.id.sensor_controller_ip_input);
        esp32PortInput = view.findViewById(R.id.esp32_port_input);
        testConnectionButton = view.findViewById(R.id.test_connection_button);
        testWifiButton = view.findViewById(R.id.test_wifi_button);
        forceWifiButton = view.findViewById(R.id.force_wifi_button);


        // Feature Switches
        wakeWordSwitch = view.findViewById(R.id.wake_word_switch);
        autoListenSwitch = view.findViewById(R.id.auto_listen_switch);
        voiceFeedbackSwitch = view.findViewById(R.id.voice_feedback_switch);
        debugModeSwitch = view.findViewById(R.id.debug_mode_switch);
        ultrasonicSensorSwitch = view.findViewById(R.id.ultrasonic_sensor_switch);
        ultrasonicFaceIntegrationSwitch = view.findViewById(R.id.ultrasonic_face_integration_switch);
        backgroundCameraSwitch = view.findViewById(R.id.background_camera_switch);
        ultrasonicDetectionDistanceInput = view.findViewById(R.id.ultrasonic_detection_distance_input);
        continuousListeningSwitch = view.findViewById(R.id.continuous_listening_switch);
        stemSimpleModeSwitch = view.findViewById(R.id.stem_simple_mode_switch);
        stemSimpleModeStatus = view.findViewById(R.id.stem_simple_mode_status);

        // Smart Greeting Settings
        faceDetectionDurationSeekBar = view.findViewById(R.id.face_detection_duration_seekbar);
        faceDetectionDurationValue = view.findViewById(R.id.face_detection_duration_value);
        greetingDistanceSeekBar = view.findViewById(R.id.greeting_distance_seekbar);
        greetingDistanceValue = view.findViewById(R.id.greeting_distance_value);
        greetingCooldownSeekBar = view.findViewById(R.id.greeting_cooldown_seekbar);
        greetingCooldownValue = view.findViewById(R.id.greeting_cooldown_value);
        handshakeDurationSeekBar = view.findViewById(R.id.handshake_duration_seekbar);
        handshakeDurationValue = view.findViewById(R.id.handshake_duration_value);
        disableSmartGreetingSwitch = view.findViewById(R.id.disable_smart_greeting_switch);

        // Walking Animation Settings
        walkingSpeedSeekBar = view.findViewById(R.id.walking_speed_seekbar);
        walkingSpeedValue = view.findViewById(R.id.walking_speed_value);
        leftArmRestSeekBar = view.findViewById(R.id.left_arm_rest_seekbar);
        leftArmRestValue = view.findViewById(R.id.left_arm_rest_value);
        rightArmRestSeekBar = view.findViewById(R.id.right_arm_rest_seekbar);
        rightArmRestValue = view.findViewById(R.id.right_arm_rest_value);
        testWalkingButton = view.findViewById(R.id.test_walking_button);

        // Action Buttons
        saveButton = view.findViewById(R.id.save_button);
        resetButton = view.findViewById(R.id.reset_button);

        // Serial Monitor Components
        serialMonitorText = view.findViewById(R.id.serial_monitor_text);
        serialMonitorScroll = view.findViewById(R.id.serial_monitor_scroll);
        serialCommandInput = view.findViewById(R.id.serial_command_input);
        btnStartMonitor = view.findViewById(R.id.btn_start_monitor);
        btnStopMonitor = view.findViewById(R.id.btn_stop_monitor);
        btnClearMonitor = view.findViewById(R.id.btn_clear_monitor);
        btnSendCommand = view.findViewById(R.id.btn_send_command);
        switchAutoScroll = view.findViewById(R.id.switch_auto_scroll);
        switchShowTimestamps = view.findViewById(R.id.switch_show_timestamps);
    }
    
    private void loadSettings() {

        // Load AI Settings
        int maxAiOutput = sharedPreferences.getInt(PREF_MAX_AI_OUTPUT, DEFAULT_MAX_AI_OUTPUT);
        maxAiOutputSeekBar.setProgress(maxAiOutput);
        maxAiOutputValue.setText(maxAiOutput + " words");

        // Load AI Context Pairs Setting
        int contextPairs = sharedPreferences.getInt("ai_context_pairs", 10);
        if (aiContextPairsSeekBar != null) {
            aiContextPairsSeekBar.setProgress(contextPairs);
            aiContextPairsValue.setText(contextPairs + " pairs");
        }
        
        // Load Voice Settings
        float speechSpeed = sharedPreferences.getFloat(PREF_SPEECH_SPEED, DEFAULT_SPEECH_SPEED);
        speechSpeedSeekBar.setProgress((int)(speechSpeed * 100));
        speechSpeedValue.setText(String.format("%.1fx", speechSpeed));

        // Load Voice Gender Setting
        String voiceGender = sharedPreferences.getString(PREF_VOICE_GENDER, DEFAULT_VOICE_GENDER);
        if (voiceGenderSpinner != null) {
            ArrayAdapter<CharSequence> adapter = ArrayAdapter.createFromResource(getContext(),
                    R.array.voice_gender_options, android.R.layout.simple_spinner_item);
            adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            voiceGenderSpinner.setAdapter(adapter);

            // Set selection based on saved preference
            int position = voiceGender.equals("male") ? 1 : 0; // 0 = female, 1 = male
            voiceGenderSpinner.setSelection(position);
        }

        int micSensitivity = sharedPreferences.getInt(PREF_MIC_SENSITIVITY, DEFAULT_MIC_SENSITIVITY);
        micSensitivitySeekBar.setProgress(micSensitivity);
        micSensitivityValue.setText(micSensitivity + "%");
        
        int micDuration = sharedPreferences.getInt(PREF_MIC_DURATION, DEFAULT_MIC_DURATION);
        micDurationSeekBar.setProgress(micDuration / 100);
        micDurationValue.setText((micDuration / 1000.0f) + "s");

        // Load Voice Command Duration Settings
        int voiceForwardDuration = sharedPreferences.getInt(PREF_VOICE_FORWARD_DURATION, 3000); // Default 3 seconds
        voiceForwardDurationSeekBar.setProgress(voiceForwardDuration / 100); // Convert from milliseconds to seekbar value
        voiceForwardDurationValue.setText(String.format("%.1fs", voiceForwardDuration / 1000.0f));

        int voiceSideDuration = sharedPreferences.getInt(PREF_VOICE_SIDE_DURATION, 2000); // Default 2 seconds
        voiceSideDurationSeekBar.setProgress(voiceSideDuration / 100); // Convert from milliseconds to seekbar value
        voiceSideDurationValue.setText(String.format("%.1fs", voiceSideDuration / 1000.0f));



        // Load ESP32 Settings
        String motorIp = sharedPreferences.getString(PREF_MOTOR_CONTROLLER_IP, DEFAULT_MOTOR_CONTROLLER_IP);
        motorControllerIpInput.setText(motorIp);

        String servoIp = sharedPreferences.getString(PREF_SERVO_CONTROLLER_IP, DEFAULT_SERVO_CONTROLLER_IP);
        servoControllerIpInput.setText(servoIp);

        String sensorIp = sharedPreferences.getString(PREF_SENSOR_CONTROLLER_IP, DEFAULT_SENSOR_CONTROLLER_IP);
        sensorControllerIpInput.setText(sensorIp);

        int esp32Port = sharedPreferences.getInt(PREF_ESP32_PORT, DEFAULT_ESP32_PORT);
        esp32PortInput.setText(String.valueOf(esp32Port));
        
        // Load Feature Settings
        wakeWordSwitch.setChecked(sharedPreferences.getBoolean(PREF_WAKE_WORD_ENABLED, true));
        autoListenSwitch.setChecked(sharedPreferences.getBoolean(PREF_AUTO_LISTEN, true));
        voiceFeedbackSwitch.setChecked(sharedPreferences.getBoolean(PREF_VOICE_FEEDBACK, true));
        debugModeSwitch.setChecked(sharedPreferences.getBoolean(PREF_DEBUG_MODE, false));
        ultrasonicSensorSwitch.setChecked(sharedPreferences.getBoolean(PREF_ULTRASONIC_SENSOR_ENABLED, true));
        ultrasonicFaceIntegrationSwitch.setChecked(sharedPreferences.getBoolean(PREF_ULTRASONIC_FACE_INTEGRATION_ENABLED, DEFAULT_ULTRASONIC_FACE_INTEGRATION_ENABLED));
        backgroundCameraSwitch.setChecked(sharedPreferences.getBoolean(PREF_BACKGROUND_CAMERA_ENABLED, true));
        ultrasonicDetectionDistanceInput.setText(String.valueOf(sharedPreferences.getInt(PREF_ULTRASONIC_DETECTION_DISTANCE, DEFAULT_ULTRASONIC_DETECTION_DISTANCE)));
        continuousListeningSwitch.setChecked(sharedPreferences.getBoolean(PREF_CONTINUOUS_LISTENING, DEFAULT_CONTINUOUS_LISTENING));

        // Load Stem-Simple Mode
        boolean stemSimpleModeEnabled = sharedPreferences.getBoolean("stem_simple_mode_enabled", false);
        stemSimpleModeSwitch.setChecked(stemSimpleModeEnabled);
        updateStemSimpleModeStatus(stemSimpleModeEnabled);

        // Load Smart Greeting Settings
        if (faceDetectionDurationSeekBar != null) {
            int duration = sharedPreferences.getInt(PREF_FACE_DETECTION_DURATION, DEFAULT_FACE_DETECTION_DURATION);
            int progress = (duration / 100) - 5; // Convert milliseconds to seekbar progress (0.5s-10s)
            faceDetectionDurationSeekBar.setProgress(Math.max(0, Math.min(95, progress)));
            faceDetectionDurationValue.setText(String.format("%.1fs", duration / 1000.0f));
        }

        if (greetingDistanceSeekBar != null) {
            int distance = sharedPreferences.getInt(PREF_GREETING_DISTANCE_THRESHOLD, DEFAULT_GREETING_DISTANCE_THRESHOLD);
            greetingDistanceSeekBar.setProgress(Math.max(0, Math.min(100, distance - 10))); // 10-110cm range
            greetingDistanceValue.setText(distance + "cm");
        }

        if (greetingCooldownSeekBar != null) {
            int cooldown = sharedPreferences.getInt(PREF_GREETING_COOLDOWN, DEFAULT_GREETING_COOLDOWN);
            int progress = (cooldown / 1000) - 5; // Convert milliseconds to seekbar progress (5s-60s)
            greetingCooldownSeekBar.setProgress(Math.max(0, Math.min(55, progress)));
            greetingCooldownValue.setText((cooldown / 1000) + "s");
        }

        if (handshakeDurationSeekBar != null) {
            int duration = sharedPreferences.getInt(PREF_HANDSHAKE_DURATION, DEFAULT_HANDSHAKE_DURATION);
            int progress = (duration / 1000) - 1; // Convert milliseconds to seekbar progress (1s-30s)
            handshakeDurationSeekBar.setProgress(Math.max(0, Math.min(29, progress)));
            handshakeDurationValue.setText((duration / 1000) + "s");
        }

        if (disableSmartGreetingSwitch != null) {
            disableSmartGreetingSwitch.setChecked(sharedPreferences.getBoolean(PREF_SMART_GREETING_DISABLED, DEFAULT_SMART_GREETING_DISABLED));
        }
        
        // Load Gemini Model Configuration
        loadGeminiModelConfiguration();
    }
    
    private void setupListeners() {

        // AI Output SeekBar
        maxAiOutputSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                maxAiOutputValue.setText(progress + " words");
            }
            @Override public void onStartTrackingTouch(SeekBar seekBar) {}
            @Override public void onStopTrackingTouch(SeekBar seekBar) {}
        });

        // AI Context Pairs SeekBar
        if (aiContextPairsSeekBar != null) {
            aiContextPairsSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
                @Override
                public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                    if (aiContextPairsValue != null) {
                        aiContextPairsValue.setText(progress + " pairs");
                    }
                }
                @Override public void onStartTrackingTouch(SeekBar seekBar) {}
                @Override public void onStopTrackingTouch(SeekBar seekBar) {}
            });
        }
        
        // Speech Speed SeekBar
        speechSpeedSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                float speed = progress / 100.0f;
                speechSpeedValue.setText(String.format("%.1fx", speed));
            }
            @Override public void onStartTrackingTouch(SeekBar seekBar) {}
            @Override public void onStopTrackingTouch(SeekBar seekBar) {}
        });

        // Voice Gender Spinner
        if (voiceGenderSpinner != null) {
            voiceGenderSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    // Position 0 = Female, Position 1 = Male
                    String selectedGender = position == 0 ? "female" : "male";
                    // Save immediately when changed
                    SharedPreferences.Editor editor = sharedPreferences.edit();
                    editor.putString(PREF_VOICE_GENDER, selectedGender);
                    editor.apply();

                    // Notify voice service about the change
                    Intent intent = new Intent("com.stemrobo.humanoid.VOICE_SETTINGS_CHANGED");
                    intent.putExtra("voice_gender", selectedGender);
                    getContext().sendBroadcast(intent);
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {}
            });
        }

        // Mic Sensitivity SeekBar
        micSensitivitySeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                micSensitivityValue.setText(progress + "%");
            }
            @Override public void onStartTrackingTouch(SeekBar seekBar) {}
            @Override public void onStopTrackingTouch(SeekBar seekBar) {}
        });
        
        // Mic Duration SeekBar
        micDurationSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                float duration = progress / 10.0f;
                micDurationValue.setText(duration + "s");
            }
            @Override public void onStartTrackingTouch(SeekBar seekBar) {}
            @Override public void onStopTrackingTouch(SeekBar seekBar) {}
        });

        // Voice Forward Duration SeekBar
        voiceForwardDurationSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                float duration = progress / 10.0f; // Convert to seconds (0-10 seconds)
                voiceForwardDurationValue.setText(String.format("%.1fs", duration));
            }
            @Override public void onStartTrackingTouch(SeekBar seekBar) {}
            @Override public void onStopTrackingTouch(SeekBar seekBar) {}
        });

        // Voice Side Duration SeekBar
        voiceSideDurationSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                float duration = progress / 10.0f; // Convert to seconds (0-10 seconds)
                voiceSideDurationValue.setText(String.format("%.1fs", duration));
            }
            @Override public void onStartTrackingTouch(SeekBar seekBar) {}
            @Override public void onStopTrackingTouch(SeekBar seekBar) {}
        });



        // Test Voice Commands Button
        testVoiceCommandsButton.setOnClickListener(v -> {
            Intent intent = new Intent(getActivity(), VoiceCommandTestActivity.class);
            startActivity(intent);
        });

        // Smart Greeting Settings SeekBars
        if (faceDetectionDurationSeekBar != null) {
            faceDetectionDurationSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
                @Override
                public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                    float duration = (progress + 5) / 10.0f; // 0.5s to 10.0s
                    faceDetectionDurationValue.setText(String.format("%.1fs", duration));
                }
                @Override public void onStartTrackingTouch(SeekBar seekBar) {}
                @Override public void onStopTrackingTouch(SeekBar seekBar) {}
            });
        }

        if (greetingDistanceSeekBar != null) {
            greetingDistanceSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
                @Override
                public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                    int distance = progress + 10; // 10cm to 110cm
                    greetingDistanceValue.setText(distance + "cm");
                }
                @Override public void onStartTrackingTouch(SeekBar seekBar) {}
                @Override public void onStopTrackingTouch(SeekBar seekBar) {}
            });
        }

        if (greetingCooldownSeekBar != null) {
            greetingCooldownSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
                @Override
                public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                    int cooldown = (progress + 5); // 5s to 60s
                    greetingCooldownValue.setText(cooldown + "s");
                }
                @Override public void onStartTrackingTouch(SeekBar seekBar) {}
                @Override public void onStopTrackingTouch(SeekBar seekBar) {}
            });
        }

        if (handshakeDurationSeekBar != null) {
            handshakeDurationSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
                @Override
                public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                    int duration = (progress + 1); // 1s to 30s
                    handshakeDurationValue.setText(duration + "s");
                }
                @Override public void onStartTrackingTouch(SeekBar seekBar) {}
                @Override public void onStopTrackingTouch(SeekBar seekBar) {}
            });
        }

        // Ultrasonic Face Integration Switch Listener
        ultrasonicFaceIntegrationSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            System.out.println(TAG + ": Ultrasonic Face Integration toggled: " + isChecked);

            if (isChecked) {
                // Validate that ultrasonic sensor is also enabled
                if (!ultrasonicSensorSwitch.isChecked()) {
                    System.out.println(TAG + ": Auto-enabling ultrasonic sensor for face integration");
                    ultrasonicSensorSwitch.setChecked(true);
                }

                // Validate distance input
                String distanceText = ultrasonicDetectionDistanceInput.getText().toString().trim();
                if (distanceText.isEmpty()) {
                    ultrasonicDetectionDistanceInput.setText(String.valueOf(DEFAULT_ULTRASONIC_DETECTION_DISTANCE));
                }

                System.out.println(TAG + ": Smart Greeting with ultrasonic + face integration enabled");
            } else {
                System.out.println(TAG + ": Smart Greeting with ultrasonic + face integration disabled");
            }
        });

        // Stem-Simple Mode Switch Listener
        stemSimpleModeSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            updateStemSimpleModeStatus(isChecked);
            Log.d(TAG, "Stem-Simple AI Mode toggled: " + isChecked);
        });

        // Save Button
        saveButton.setOnClickListener(v -> saveSettings());
        
        // Reset Button
        resetButton.setOnClickListener(v -> resetToDefaults());

        // Test Connection Buttons
        testConnectionButton.setOnClickListener(v -> testESP32Connections());
        testWifiButton.setOnClickListener(v -> testWiFiConnection());
        forceWifiButton.setOnClickListener(v -> forceWiFiMode());

        // Manual Communication Mode Toggle Button
        btnToggleCommunicationMode.setOnClickListener(v -> toggleCommunicationMode());

        // USB Debug Buttons
        btnScanUsb.setOnClickListener(v -> scanForUSBDevices());
        btnUsbDebug.setOnClickListener(v -> showUSBDebugInfo());
        btnFixUsb.setOnClickListener(v -> fixUSBConnection());

        // Walking Animation Settings SeekBars
        if (walkingSpeedSeekBar != null) {
            walkingSpeedSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
                @Override
                public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                    int timing = (progress + 2) * 100; // 200ms to 3000ms (2-30 * 100)
                    walkingSpeedValue.setText(timing + "ms");

                    // Send to ESP32 immediately when changed
                    if (fromUser) {
                        ESP32CommunicationManager commManager = ESP32CommunicationManager.getInstance();
                        commManager.sendServoCommand("SET_WALK_TIMING:" + timing);
                    }
                }
                @Override public void onStartTrackingTouch(SeekBar seekBar) {}
                @Override public void onStopTrackingTouch(SeekBar seekBar) {}
            });
        }

        if (leftArmRestSeekBar != null) {
            leftArmRestSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
                @Override
                public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                    leftArmRestValue.setText(progress + "°");

                    // Send to ESP32 immediately when changed
                    if (fromUser) {
                        ESP32CommunicationManager commManager = ESP32CommunicationManager.getInstance();
                        commManager.sendServoCommand("SET_LEFT_REST:" + progress);
                    }
                }
                @Override public void onStartTrackingTouch(SeekBar seekBar) {}
                @Override public void onStopTrackingTouch(SeekBar seekBar) {}
            });
        }

        if (rightArmRestSeekBar != null) {
            rightArmRestSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
                @Override
                public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                    rightArmRestValue.setText(progress + "°");

                    // Send to ESP32 immediately when changed
                    if (fromUser) {
                        ESP32CommunicationManager commManager = ESP32CommunicationManager.getInstance();
                        commManager.sendServoCommand("SET_RIGHT_REST:" + progress);
                    }
                }
                @Override public void onStartTrackingTouch(SeekBar seekBar) {}
                @Override public void onStopTrackingTouch(SeekBar seekBar) {}
            });
        }

        // Test Walking Animation Button
        if (testWalkingButton != null) {
            testWalkingButton.setOnClickListener(v -> {
                ESP32CommunicationManager commManager = ESP32CommunicationManager.getInstance();
                // Start walking for 5 seconds to test
                commManager.sendMotorCommand("F"); // Start forward movement

                // Stop after 5 seconds
                new Handler(Looper.getMainLooper()).postDelayed(() -> {
                    commManager.sendMotorCommand("S"); // Stop movement
                    Toast.makeText(getContext(), "Walking test completed", Toast.LENGTH_SHORT).show();
                }, 5000);

                Toast.makeText(getContext(), "Testing walking animation for 5 seconds...", Toast.LENGTH_SHORT).show();
            });
        }
        
        // Gemini Model Configuration Listeners
        setupGeminiModelListeners();

        // Serial Monitor Buttons
        btnStartMonitor.setOnClickListener(v -> startSerialMonitor());
        btnStopMonitor.setOnClickListener(v -> stopSerialMonitor());
        btnClearMonitor.setOnClickListener(v -> clearSerialMonitor());
        btnSendCommand.setOnClickListener(v -> sendSerialCommand());
    }
    
    private void saveSettings() {
        try {
            SharedPreferences.Editor editor = sharedPreferences.edit();



            // Save AI Settings
            editor.putInt(PREF_MAX_AI_OUTPUT, maxAiOutputSeekBar.getProgress());

            // Save AI Context Pairs Setting
            if (aiContextPairsSeekBar != null) {
                editor.putInt("ai_context_pairs", aiContextPairsSeekBar.getProgress());
            }
            
            // Save Voice Settings
            editor.putFloat(PREF_SPEECH_SPEED, speechSpeedSeekBar.getProgress() / 100.0f);

            // Save Voice Gender Setting
            if (voiceGenderSpinner != null) {
                String selectedGender = voiceGenderSpinner.getSelectedItemPosition() == 0 ? "female" : "male";
                editor.putString(PREF_VOICE_GENDER, selectedGender);
            }

            editor.putInt(PREF_MIC_SENSITIVITY, micSensitivitySeekBar.getProgress());
            editor.putInt(PREF_MIC_DURATION, micDurationSeekBar.getProgress() * 100);

            // Save Voice Command Duration Settings
            editor.putInt(PREF_VOICE_FORWARD_DURATION, (int)(voiceForwardDurationSeekBar.getProgress() * 100)); // Convert to milliseconds
            editor.putInt(PREF_VOICE_SIDE_DURATION, (int)(voiceSideDurationSeekBar.getProgress() * 100)); // Convert to milliseconds


            
            // Save ESP32 Settings
            editor.putString(PREF_MOTOR_CONTROLLER_IP, motorControllerIpInput.getText().toString().trim());
            editor.putString(PREF_SERVO_CONTROLLER_IP, servoControllerIpInput.getText().toString().trim());
            editor.putString(PREF_SENSOR_CONTROLLER_IP, sensorControllerIpInput.getText().toString().trim());
            editor.putInt(PREF_ESP32_PORT, Integer.parseInt(esp32PortInput.getText().toString().trim()));
            
            // Save Feature Settings
            editor.putBoolean(PREF_WAKE_WORD_ENABLED, wakeWordSwitch.isChecked());
            editor.putBoolean(PREF_AUTO_LISTEN, autoListenSwitch.isChecked());
            editor.putBoolean(PREF_VOICE_FEEDBACK, voiceFeedbackSwitch.isChecked());
            editor.putBoolean(PREF_DEBUG_MODE, debugModeSwitch.isChecked());
            editor.putBoolean(PREF_ULTRASONIC_SENSOR_ENABLED, ultrasonicSensorSwitch.isChecked());
            editor.putBoolean(PREF_ULTRASONIC_FACE_INTEGRATION_ENABLED, ultrasonicFaceIntegrationSwitch.isChecked());
            editor.putBoolean(PREF_BACKGROUND_CAMERA_ENABLED, backgroundCameraSwitch.isChecked());
            editor.putBoolean(PREF_CONTINUOUS_LISTENING, continuousListeningSwitch.isChecked());

            // Save Stem-Simple Mode
            editor.putBoolean("stem_simple_mode_enabled", stemSimpleModeSwitch.isChecked());

            // Save ultrasonic detection distance
            try {
                int distance = Integer.parseInt(ultrasonicDetectionDistanceInput.getText().toString().trim());
                editor.putInt(PREF_ULTRASONIC_DETECTION_DISTANCE, distance);
            } catch (NumberFormatException e) {
                // Use default if invalid input
                editor.putInt(PREF_ULTRASONIC_DETECTION_DISTANCE, DEFAULT_ULTRASONIC_DETECTION_DISTANCE);
            }

            // Save Smart Greeting Settings
            if (faceDetectionDurationSeekBar != null) {
                int duration = (faceDetectionDurationSeekBar.getProgress() + 5) * 100; // Convert to milliseconds
                editor.putInt(PREF_FACE_DETECTION_DURATION, duration);
            }

            if (greetingDistanceSeekBar != null) {
                int distance = greetingDistanceSeekBar.getProgress() + 10; // 10-110cm range
                editor.putInt(PREF_GREETING_DISTANCE_THRESHOLD, distance);
            }

            if (greetingCooldownSeekBar != null) {
                int cooldown = (greetingCooldownSeekBar.getProgress() + 5) * 1000; // Convert to milliseconds
                editor.putInt(PREF_GREETING_COOLDOWN, cooldown);
            }

            if (handshakeDurationSeekBar != null) {
                int duration = (handshakeDurationSeekBar.getProgress() + 1) * 1000; // Convert to milliseconds
                editor.putInt(PREF_HANDSHAKE_DURATION, duration);
            }

            if (disableSmartGreetingSwitch != null) {
                editor.putBoolean(PREF_SMART_GREETING_DISABLED, disableSmartGreetingSwitch.isChecked());
            }
            
            // Save Gemini Model configurations
            saveGeminiModelConfigurations();

            editor.apply();
            
            // Refresh IP addresses in communication manager
            ESP32CommunicationManager.getInstance().refreshIPAddressesFromSettings();
            
            Toast.makeText(getContext(), "Settings saved successfully!", Toast.LENGTH_SHORT).show();
            
        } catch (Exception e) {
            Toast.makeText(getContext(), "Error saving settings: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }
    
    private void resetToDefaults() {
        // Reset AI Settings
        maxAiOutputSeekBar.setProgress(DEFAULT_MAX_AI_OUTPUT);
        maxAiOutputValue.setText(DEFAULT_MAX_AI_OUTPUT + " words");
        
        // Reset Voice Settings
        speechSpeedSeekBar.setProgress((int)(DEFAULT_SPEECH_SPEED * 100));
        speechSpeedValue.setText(String.format("%.1fx", DEFAULT_SPEECH_SPEED));

        // Reset Voice Gender Setting
        if (voiceGenderSpinner != null) {
            int defaultPosition = DEFAULT_VOICE_GENDER.equals("male") ? 1 : 0;
            voiceGenderSpinner.setSelection(defaultPosition);
        }

        micSensitivitySeekBar.setProgress(DEFAULT_MIC_SENSITIVITY);
        micSensitivityValue.setText(DEFAULT_MIC_SENSITIVITY + "%");
        micDurationSeekBar.setProgress(DEFAULT_MIC_DURATION / 100);
        micDurationValue.setText((DEFAULT_MIC_DURATION / 1000.0f) + "s");
        
        // Reset ESP32 Settings
        motorControllerIpInput.setText(DEFAULT_MOTOR_CONTROLLER_IP);
        servoControllerIpInput.setText(DEFAULT_SERVO_CONTROLLER_IP);
        sensorControllerIpInput.setText(DEFAULT_SENSOR_CONTROLLER_IP);
        esp32PortInput.setText(String.valueOf(DEFAULT_ESP32_PORT));
        
        // Reset Feature Settings
        wakeWordSwitch.setChecked(true);
        autoListenSwitch.setChecked(true);
        voiceFeedbackSwitch.setChecked(true);
        debugModeSwitch.setChecked(false);
        ultrasonicSensorSwitch.setChecked(true);
        ultrasonicFaceIntegrationSwitch.setChecked(DEFAULT_ULTRASONIC_FACE_INTEGRATION_ENABLED);
        backgroundCameraSwitch.setChecked(true);
        ultrasonicDetectionDistanceInput.setText(String.valueOf(DEFAULT_ULTRASONIC_DETECTION_DISTANCE));
        continuousListeningSwitch.setChecked(DEFAULT_CONTINUOUS_LISTENING);
        
        Toast.makeText(getContext(), "Settings reset to defaults", Toast.LENGTH_SHORT).show();
    }

    private void testESP32Connections() {
        // Update ESP32CommunicationManager with current IP addresses
        String motorIP = motorControllerIpInput.getText().toString().trim();
        String servoIP = servoControllerIpInput.getText().toString().trim();
        String sensorIP = sensorControllerIpInput.getText().toString().trim();

        ESP32CommunicationManager.getInstance().updateIPAddresses(motorIP, servoIP, sensorIP);

        // Show testing dialog
        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(getContext());
        builder.setTitle("Testing ESP32 Connections");
        builder.setMessage("Testing connections to all ESP32 controllers...");
        builder.setCancelable(false);
        android.app.AlertDialog testDialog = builder.create();
        testDialog.show();

        // Test all connections
        ESP32CommunicationManager.getInstance().testAllConnections(new ESP32CommunicationManager.ConnectionTestListener() {
            @Override
            public void onTestResult(String controllerId, boolean success, String message) {
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        String status = success ? "✓" : "✗";
                        String resultMessage = controllerId.toUpperCase() + " Controller: " + status + " " + message;

                        // Update dialog message
                        testDialog.setMessage(testDialog.getContext().getString(android.R.string.ok) + "\n" + resultMessage);
                    });
                }
            }

            @Override
            public void onAllTestsComplete(Map<String, Boolean> results) {
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        testDialog.dismiss();

                        // Show final results
                        StringBuilder resultText = new StringBuilder("Connection Test Results:\n\n");
                        resultText.append("Motor Controller: ").append(results.get("motor") ? "✓ Connected" : "✗ Failed").append("\n");
                        resultText.append("Servo Controller: ").append(results.get("servo") ? "✓ Connected" : "✗ Failed").append("\n");
                        resultText.append("Sensor Controller: ").append(results.get("sensor") ? "✓ Connected" : "✗ Failed").append("\n");

                        int connectedCount = 0;
                        for (Boolean connected : results.values()) {
                            if (connected) connectedCount++;
                        }

                        resultText.append("\nConnected: ").append(connectedCount).append("/3 controllers");

                        android.app.AlertDialog.Builder resultBuilder = new android.app.AlertDialog.Builder(getContext());
                        resultBuilder.setTitle("Connection Test Complete");
                        resultBuilder.setMessage(resultText.toString());
                        resultBuilder.setPositiveButton("OK", null);
                        resultBuilder.show();
                    });
                }
            }
        });
    }

    private void testWiFiConnection() {
        // Update ESP32CommunicationManager with current IP addresses
        String motorIP = motorControllerIpInput.getText().toString().trim();
        String servoIP = servoControllerIpInput.getText().toString().trim();
        String sensorIP = sensorControllerIpInput.getText().toString().trim();

        ESP32CommunicationManager.getInstance().updateIPAddresses(motorIP, servoIP, sensorIP);

        // Show testing dialog
        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(getContext());
        builder.setTitle("Testing WiFi Connection");
        builder.setMessage("Testing WiFi connection to ESP32...");
        builder.setCancelable(false);
        android.app.AlertDialog testDialog = builder.create();
        testDialog.show();

        // Test WiFi connection
        ESP32CommunicationManager.getInstance().testWiFiConnection(new ESP32CommunicationManager.SingleConnectionTestListener() {
            @Override
            public void onTestResult(boolean success, String message) {
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        testDialog.dismiss();
                        
                        String status = success ? "✓" : "✗";
                        String title = success ? "WiFi Connection Successful" : "WiFi Connection Failed";
                        String resultMessage = status + " " + message + "\n\nIP: " + motorIP;

                        android.app.AlertDialog.Builder resultBuilder = new android.app.AlertDialog.Builder(getContext());
                        resultBuilder.setTitle(title);
                        resultBuilder.setMessage(resultMessage);
                        resultBuilder.setPositiveButton("OK", null);
                        resultBuilder.show();
                    });
                }
            }
        });
    }

    private void forceWiFiMode() {
        android.app.AlertDialog.Builder builder = new android.app.AlertDialog.Builder(getContext());
        builder.setTitle("Force WiFi Mode");
        builder.setMessage("This will force the robot to use WiFi communication instead of USB. " +
                          "Make sure the ESP32 IP address is configured correctly.\n\n" +
                          "Current IP: " + motorControllerIpInput.getText().toString().trim() + "\n\n" +
                          "Continue?");
        builder.setPositiveButton("Force WiFi", (dialog, which) -> {
            // Update IP addresses first
            String motorIP = motorControllerIpInput.getText().toString().trim();
            String servoIP = servoControllerIpInput.getText().toString().trim();
            String sensorIP = sensorControllerIpInput.getText().toString().trim();

            ESP32CommunicationManager.getInstance().updateIPAddresses(motorIP, servoIP, sensorIP);
            
            // Force WiFi mode
            ESP32CommunicationManager.getInstance().forceWiFiMode();
            
            Toast.makeText(getContext(), "Forced WiFi mode. Commands will now use WiFi.", Toast.LENGTH_LONG).show();
            
            // Update the display
            updateConnectionModeDisplay();
        });
        builder.setNegativeButton("Cancel", null);
        builder.show();
    }

    /**
     * Get current settings values for use by other components
     */
    public static class Settings {
        public static int getMaxAiOutput(SharedPreferences prefs) {
            return prefs.getInt(PREF_MAX_AI_OUTPUT, DEFAULT_MAX_AI_OUTPUT);
        }
        
        public static float getSpeechSpeed(SharedPreferences prefs) {
            return prefs.getFloat(PREF_SPEECH_SPEED, DEFAULT_SPEECH_SPEED);
        }

        public static String getVoiceGender(SharedPreferences prefs) {
            return prefs.getString(PREF_VOICE_GENDER, DEFAULT_VOICE_GENDER);
        }

        public static int getMicSensitivity(SharedPreferences prefs) {
            return prefs.getInt(PREF_MIC_SENSITIVITY, DEFAULT_MIC_SENSITIVITY);
        }
        
        public static int getMicDuration(SharedPreferences prefs) {
            return prefs.getInt(PREF_MIC_DURATION, DEFAULT_MIC_DURATION);
        }
        
        public static String getMotorControllerIp(SharedPreferences prefs) {
            return prefs.getString(PREF_MOTOR_CONTROLLER_IP, DEFAULT_MOTOR_CONTROLLER_IP);
        }

        public static String getServoControllerIp(SharedPreferences prefs) {
            return prefs.getString(PREF_SERVO_CONTROLLER_IP, DEFAULT_SERVO_CONTROLLER_IP);
        }

        public static String getSensorControllerIp(SharedPreferences prefs) {
            return prefs.getString(PREF_SENSOR_CONTROLLER_IP, DEFAULT_SENSOR_CONTROLLER_IP);
        }

        public static int getEsp32Port(SharedPreferences prefs) {
            return prefs.getInt(PREF_ESP32_PORT, DEFAULT_ESP32_PORT);
        }
        
        public static boolean isWakeWordEnabled(SharedPreferences prefs) {
            return prefs.getBoolean(PREF_WAKE_WORD_ENABLED, true);
        }
        
        public static boolean isAutoListenEnabled(SharedPreferences prefs) {
            return prefs.getBoolean(PREF_AUTO_LISTEN, true);
        }
        
        public static boolean isVoiceFeedbackEnabled(SharedPreferences prefs) {
            return prefs.getBoolean(PREF_VOICE_FEEDBACK, true);
        }
        
        public static boolean isDebugModeEnabled(SharedPreferences prefs) {
            return prefs.getBoolean(PREF_DEBUG_MODE, false);
        }

        public static boolean isUltrasonicSensorEnabled(SharedPreferences prefs) {
            return prefs.getBoolean(PREF_ULTRASONIC_SENSOR_ENABLED, true);
        }

        public static boolean isBackgroundCameraEnabled(SharedPreferences prefs) {
            return prefs.getBoolean(PREF_BACKGROUND_CAMERA_ENABLED, true);
        }
    }

    private void updateConnectionModeDisplay() {
        ESP32CommunicationManager commManager = ESP32CommunicationManager.getInstance();

        if (connectionModeIndicator != null && connectionIpDisplay != null && btnToggleCommunicationMode != null) {
            boolean usbConnected = commManager.isUsbConnected();
            ESP32CommunicationManager.CommunicationMode currentMode = commManager.getCurrentMode();

            if (currentMode == ESP32CommunicationManager.CommunicationMode.USB_UART) {
                connectionModeIndicator.setText("🔌 USB Mode Active");
                connectionModeIndicator.setTextColor(getResources().getColor(R.color.status_connected));
                connectionIpDisplay.setText("Direct USB");
                btnToggleCommunicationMode.setText("Switch to WiFi");
            } else {
                connectionModeIndicator.setText("📶 WiFi Mode Active");
                connectionModeIndicator.setTextColor(getResources().getColor(R.color.status_disconnected));

                // Show current IP address
                String motorIp = motorControllerIpInput != null ? motorControllerIpInput.getText().toString().trim() : "";
                if (!motorIp.isEmpty()) {
                    connectionIpDisplay.setText(motorIp);
                } else {
                    connectionIpDisplay.setText("No IP Set");
                }
                btnToggleCommunicationMode.setText("Switch to USB");
            }

            // Update button state based on USB availability
            if (!usbConnected && currentMode == ESP32CommunicationManager.CommunicationMode.USB_UART) {
                btnToggleCommunicationMode.setText("USB Not Available");
                btnToggleCommunicationMode.setEnabled(false);
            } else {
                btnToggleCommunicationMode.setEnabled(true);
            }
        }
    }

    private void toggleCommunicationMode() {
        ESP32CommunicationManager commManager = ESP32CommunicationManager.getInstance();
        ESP32CommunicationManager.CommunicationMode currentMode = commManager.getCurrentMode();

        try {
            if (currentMode == ESP32CommunicationManager.CommunicationMode.USB_UART) {
                // Switch to WiFi mode
                commManager.forceWiFiMode();
                Log.d(TAG, "Manually switched to WiFi mode");
                if (getContext() != null) {
                    Toast.makeText(getContext(), "Switched to WiFi mode", Toast.LENGTH_SHORT).show();
                }
            } else {
                // Switch to USB mode
                if (commManager.isUsbConnected()) {
                    commManager.forceUSBMode();
                    Log.d(TAG, "Manually switched to USB mode");
                    if (getContext() != null) {
                        Toast.makeText(getContext(), "Switched to USB mode", Toast.LENGTH_SHORT).show();
                    }
                } else {
                    Log.w(TAG, "Cannot switch to USB mode - USB not connected");
                    if (getContext() != null) {
                        Toast.makeText(getContext(), "USB not connected - cannot switch to USB mode", Toast.LENGTH_LONG).show();
                    }
                }
            }

            // Update display after mode change
            updateConnectionModeDisplay();

        } catch (Exception e) {
            Log.e(TAG, "Error toggling communication mode: " + e.getMessage());
            if (getContext() != null) {
                Toast.makeText(getContext(), "Error switching communication mode", Toast.LENGTH_SHORT).show();
            }
        }
    }

    private void scanForUSBDevices() {
        ESP32CommunicationManager commManager = ESP32CommunicationManager.getInstance();
        try {
            commManager.scanForUSBDevices();
            Toast.makeText(getContext(), "USB scan initiated - check logs", Toast.LENGTH_SHORT).show();

            // Update display after scan
            updateConnectionModeDisplay();

        } catch (Exception e) {
            Log.e(TAG, "Error scanning for USB devices: " + e.getMessage());
            Toast.makeText(getContext(), "USB scan failed: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private void showUSBDebugInfo() {
        ESP32CommunicationManager commManager = ESP32CommunicationManager.getInstance();
        try {
            String debugInfo = commManager.getUSBDebugInfo();
            Log.d(TAG, debugInfo);

            // Show debug info in dialog
            new AlertDialog.Builder(getContext())
                .setTitle("USB Debug Information")
                .setMessage(debugInfo)
                .setPositiveButton("OK", null)
                .show();

        } catch (Exception e) {
            Log.e(TAG, "Error getting USB debug info: " + e.getMessage());
            Toast.makeText(getContext(), "Debug info failed: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private void fixUSBConnection() {
        ESP32CommunicationManager commManager = ESP32CommunicationManager.getInstance();
        try {
            Log.d(TAG, "=== FIXING USB CONNECTION ===");

            // Show progress dialog
            new AlertDialog.Builder(getContext())
                .setTitle("Fixing USB Connection")
                .setMessage("Re-initializing USB Serial Manager...\nPlease wait...")
                .setCancelable(false)
                .show();

            // Force re-initialization in background
            new Thread(() -> {
                try {
                    commManager.reinitializeUSBSerialService();

                    // Update UI on main thread
                    if (getActivity() != null) {
                        getActivity().runOnUiThread(() -> {
                            updateConnectionModeDisplay();

                            String status = commManager.isUsbConnected() ?
                                "USB connection fixed successfully!" :
                                "USB connection still not available. Check cable and permissions.";

                            new AlertDialog.Builder(getContext())
                                .setTitle("USB Fix Complete")
                                .setMessage(status)
                                .setPositiveButton("OK", null)
                                .show();
                        });
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error during USB fix: " + e.getMessage());
                    if (getActivity() != null) {
                        getActivity().runOnUiThread(() -> {
                            new AlertDialog.Builder(getContext())
                                .setTitle("USB Fix Failed")
                                .setMessage("Failed to fix USB connection: " + e.getMessage())
                                .setPositiveButton("OK", null)
                                .show();
                        });
                    }
                }
            }).start();

        } catch (Exception e) {
            Log.e(TAG, "Error fixing USB connection: " + e.getMessage());
            Toast.makeText(getContext(), "USB fix failed: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }



    @Override
    public void onResume() {
        super.onResume();
        // Update connection mode display when fragment resumes
        updateConnectionModeDisplay();
    }

    // Serial Monitor Methods
    private void startSerialMonitor() {
        if (isMonitoringActive) {
            Toast.makeText(getContext(), "Serial monitor is already running", Toast.LENGTH_SHORT).show();
            return;
        }

        isMonitoringActive = true;
        serialBuffer.setLength(0);

        // Update UI
        appendToSerialMonitor("=== Serial Monitor Started ===");
        btnStartMonitor.setEnabled(false);
        btnStopMonitor.setEnabled(true);

        // Start monitoring ESP32 communication
        ESP32CommunicationManager commManager = ESP32CommunicationManager.getInstance();
        commManager.setCommunicationListener(new ESP32CommunicationManager.CommunicationListener() {
            @Override
            public void onCommandSent(String controllerId, com.stemrobo.humanoid.models.RobotCommand command) {
                if (isMonitoringActive) {
                    String timestamp = switchShowTimestamps.isChecked() ?
                        "[" + java.text.DateFormat.getTimeInstance().format(new java.util.Date()) + "] " : "";
                    appendToSerialMonitor(timestamp + "SENT -> " + controllerId + ": " + command.getAction());
                }
            }

            @Override
            public void onResponseReceived(String controllerId, com.stemrobo.humanoid.models.RobotResponse response) {
                if (isMonitoringActive) {
                    String timestamp = switchShowTimestamps.isChecked() ?
                        "[" + java.text.DateFormat.getTimeInstance().format(new java.util.Date()) + "] " : "";
                    appendToSerialMonitor(timestamp + "RECV <- " + controllerId + ": " + response.getMessage());
                }
            }

            @Override
            public void onConnectionStatusChanged(String controllerId, boolean connected) {
                if (isMonitoringActive) {
                    String timestamp = switchShowTimestamps.isChecked() ?
                        "[" + java.text.DateFormat.getTimeInstance().format(new java.util.Date()) + "] " : "";
                    appendToSerialMonitor(timestamp + "CONN: " + controllerId + " " + (connected ? "CONNECTED" : "DISCONNECTED"));
                }
            }

            @Override
            public void onError(String controllerId, String error) {
                if (isMonitoringActive) {
                    String timestamp = switchShowTimestamps.isChecked() ?
                        "[" + java.text.DateFormat.getTimeInstance().format(new java.util.Date()) + "] " : "";
                    appendToSerialMonitor(timestamp + "ERROR: " + controllerId + " - " + error);
                }
            }
        });

        Toast.makeText(getContext(), "Serial monitor started", Toast.LENGTH_SHORT).show();
    }

    private void stopSerialMonitor() {
        isMonitoringActive = false;

        // Update UI
        appendToSerialMonitor("=== Serial Monitor Stopped ===");
        btnStartMonitor.setEnabled(true);
        btnStopMonitor.setEnabled(false);

        // Remove listener
        ESP32CommunicationManager commManager = ESP32CommunicationManager.getInstance();
        commManager.setCommunicationListener(null);

        Toast.makeText(getContext(), "Serial monitor stopped", Toast.LENGTH_SHORT).show();
    }

    private void clearSerialMonitor() {
        serialBuffer.setLength(0);
        serialMonitorText.setText("Serial Monitor - Ready\nClick 'Start Monitor' to begin monitoring ESP32 communication...");

        if (switchAutoScroll.isChecked()) {
            serialMonitorScroll.post(() -> serialMonitorScroll.fullScroll(ScrollView.FOCUS_DOWN));
        }

        Toast.makeText(getContext(), "Serial monitor cleared", Toast.LENGTH_SHORT).show();
    }

    private void sendSerialCommand() {
        String command = serialCommandInput.getText().toString().trim();
        if (command.isEmpty()) {
            Toast.makeText(getContext(), "Please enter a command", Toast.LENGTH_SHORT).show();
            return;
        }

        // Send command to ESP32
        ESP32CommunicationManager commManager = ESP32CommunicationManager.getInstance();

        // Determine command type and send appropriately
        if (command.matches("[FBLRS]|STOP_ALL|WAVE|POINT|REST|HELLO|HUMAN_DETECT_ON|HUMAN_DETECT_OFF|GET_DISTANCE|STATUS")) {
            if (command.matches("[FBLRS]|STOP_ALL")) {
                commManager.sendMotorCommand(command);
            } else if (command.matches("WAVE|POINT|REST|HELLO")) {
                commManager.sendServoCommand(command);
            } else {
                commManager.sendSensorCommand(command, null);
            }

            // Log the sent command
            String timestamp = switchShowTimestamps.isChecked() ?
                "[" + java.text.DateFormat.getTimeInstance().format(new java.util.Date()) + "] " : "";
            appendToSerialMonitor(timestamp + "MANUAL -> " + command);

            // Clear input
            serialCommandInput.setText("");

        } else {
            Toast.makeText(getContext(), "Invalid command. Valid commands: F,B,L,R,S,STOP_ALL,WAVE,POINT,REST,HELLO,HUMAN_DETECT_ON,HUMAN_DETECT_OFF,GET_DISTANCE,STATUS", Toast.LENGTH_LONG).show();
        }
    }

    private void appendToSerialMonitor(String message) {
        uiHandler.post(() -> {
            serialBuffer.append(message).append("\n");

            // Limit buffer size to prevent memory issues
            if (serialBuffer.length() > 10000) {
                serialBuffer.delete(0, 2000); // Remove first 2000 characters
            }

            serialMonitorText.setText(serialBuffer.toString());

            // Auto-scroll to bottom if enabled
            if (switchAutoScroll.isChecked()) {
                serialMonitorScroll.post(() -> serialMonitorScroll.fullScroll(ScrollView.FOCUS_DOWN));
            }
        });
    }


    
    /**
     * Load Gemini model configuration settings
     */
    private void loadGeminiModelConfiguration() {
        try {
            // Setup model spinner
            if (geminiModelSpinner != null) {
                ArrayAdapter<String> adapter = new ArrayAdapter<>(getContext(),
                    android.R.layout.simple_spinner_item, modelConfig.getAvailableModelNames());
                adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
                geminiModelSpinner.setAdapter(adapter);
                
                // Set current selection
                int currentModel = modelConfig.getCurrentModel();
                geminiModelSpinner.setSelection(currentModel - 1); // Convert to 0-based index
            }
            
            // Load model configurations
            if (model1NameInput != null) {
                model1NameInput.setText(modelConfig.getModelName(GeminiModelConfig.MODEL_1));
            }
            if (model1ApiKeyInput != null) {
                model1ApiKeyInput.setText(modelConfig.getApiKey(GeminiModelConfig.MODEL_1));
            }
            if (model1EndpointInput != null) {
                model1EndpointInput.setText(modelConfig.getEndpoint(GeminiModelConfig.MODEL_1));
            }
            
            if (model2NameInput != null) {
                model2NameInput.setText(modelConfig.getModelName(GeminiModelConfig.MODEL_2));
            }
            if (model2ApiKeyInput != null) {
                model2ApiKeyInput.setText(modelConfig.getApiKey(GeminiModelConfig.MODEL_2));
            }
            if (model2EndpointInput != null) {
                model2EndpointInput.setText(modelConfig.getEndpoint(GeminiModelConfig.MODEL_2));
            }
            
            // Update status
            updateModelStatus();
            
        } catch (Exception e) {
            Log.e(TAG, "Error loading Gemini model configuration", e);
        }
    }
    
    /**
     * Setup Gemini model configuration listeners
     */
    private void setupGeminiModelListeners() {
        try {
            // Model selection spinner
            if (geminiModelSpinner != null) {
                geminiModelSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                    @Override
                    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                        int modelId = position + 1; // Convert to 1-based model ID
                        modelConfig.setCurrentModel(modelId);
                        geminiAIService.switchModel(modelId);
                        updateModelStatus();
                        
                        Toast.makeText(getContext(), "Switched to " + modelConfig.getCurrentModelName(), 
                                     Toast.LENGTH_SHORT).show();
                    }
                    
                    @Override
                    public void onNothingSelected(AdapterView<?> parent) {}
                });
            }
            
            // Test model buttons
            if (testModel1Button != null) {
                testModel1Button.setOnClickListener(v -> testGeminiModel(GeminiModelConfig.MODEL_1));
            }
            
            if (testModel2Button != null) {
                testModel2Button.setOnClickListener(v -> testGeminiModel(GeminiModelConfig.MODEL_2));
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error setting up Gemini model listeners", e);
        }
    }
    
    /**
     * Update model status display
     */
    private void updateModelStatus() {
        if (geminiModelStatus != null) {
            String currentModelName = modelConfig.getCurrentModelName();
            boolean isConfigured = modelConfig.isCurrentModelConfigured();
            
            String status = isConfigured ? 
                "✅ Current: " + currentModelName :
                "❌ Current: " + currentModelName + " (API key required)";
            
            geminiModelStatus.setText(status);
        }
    }
    
    /**
     * Test specified Gemini model
     */
    private void testGeminiModel(int modelId) {
        String modelName = modelConfig.getModelName(modelId);
        
        // Save current configurations first
        saveGeminiModelConfigurations();
        
        // Check if model is configured
        if (!modelConfig.isApiKeyConfigured(modelId)) {
            Toast.makeText(getContext(), "Please configure API key for " + modelName, 
                         Toast.LENGTH_LONG).show();
            return;
        }
        
        // Show testing dialog
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext());
        builder.setTitle("Testing " + modelName);
        builder.setMessage("Testing connection to " + modelName + "...");
        builder.setCancelable(false);
        AlertDialog testDialog = builder.create();
        testDialog.show();
        
        // Switch to test model temporarily
        int originalModel = modelConfig.getCurrentModel();
        modelConfig.setCurrentModel(modelId);
        
        // Test with simple query
        geminiAIService.getAIResponse("Hello, can you hear me?", new GeminiAIService.AIResponseCallback() {
            @Override
            public void onSuccess(String response) {
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        testDialog.dismiss();
                        
                        AlertDialog.Builder resultBuilder = new AlertDialog.Builder(getContext());
                        resultBuilder.setTitle("✅ " + modelName + " Test Successful");
                        resultBuilder.setMessage("Response: " + response.substring(0, Math.min(200, response.length())) + 
                                              (response.length() > 200 ? "..." : ""));
                        resultBuilder.setPositiveButton("OK", null);
                        resultBuilder.show();
                        
                        // Restore original model
                        modelConfig.setCurrentModel(originalModel);
                        updateModelStatus();
                    });
                }
            }
            
            @Override
            public void onError(String error) {
                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        testDialog.dismiss();
                        
                        AlertDialog.Builder errorBuilder = new AlertDialog.Builder(getContext());
                        errorBuilder.setTitle("❌ " + modelName + " Test Failed");
                        errorBuilder.setMessage("Error: " + error);
                        errorBuilder.setPositiveButton("OK", null);
                        errorBuilder.show();
                        
                        // Restore original model
                        modelConfig.setCurrentModel(originalModel);
                        updateModelStatus();
                    });
                }
            }
        });
    }
    
    /**
     * Save Gemini model configurations
     */
    private void saveGeminiModelConfigurations() {
        try {
            // Save Model 1 configuration
            if (model1NameInput != null && model1ApiKeyInput != null && model1EndpointInput != null) {
                String name = model1NameInput.getText().toString().trim();
                String apiKey = model1ApiKeyInput.getText().toString().trim();
                String endpoint = model1EndpointInput.getText().toString().trim();
                
                if (!name.isEmpty() && !apiKey.isEmpty() && !endpoint.isEmpty()) {
                    modelConfig.setModel1Config(name, apiKey, endpoint);
                }
            }
            
            // Save Model 2 configuration
            if (model2NameInput != null && model2ApiKeyInput != null && model2EndpointInput != null) {
                String name = model2NameInput.getText().toString().trim();
                String apiKey = model2ApiKeyInput.getText().toString().trim();
                String endpoint = model2EndpointInput.getText().toString().trim();
                
                if (!name.isEmpty() && !apiKey.isEmpty() && !endpoint.isEmpty()) {
                    modelConfig.setModel2Config(name, apiKey, endpoint);
                }
            }
            
            updateModelStatus();
            
        } catch (Exception e) {
            Log.e(TAG, "Error saving Gemini model configurations", e);
        }
    }

    /**
     * Update the stem-simple mode status display
     */
    private void updateStemSimpleModeStatus(boolean enabled) {
        if (stemSimpleModeStatus != null) {
            if (enabled) {
                stemSimpleModeStatus.setText("✅ Stem-Simple Mode Active - Direct Voice → AI → JSON Commands");
                stemSimpleModeStatus.setTextColor(getResources().getColor(R.color.robot_primary));
            } else {
                stemSimpleModeStatus.setText("❌ Standard Mode Active - Traditional Chat Interface");
                stemSimpleModeStatus.setTextColor(getResources().getColor(R.color.text_secondary));
            }
        }
    }
}
