package com.stemrobo.humanoid.fragments;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.stemrobo.humanoid.R;
import com.stemrobo.humanoid.activities.WorkingObjectDetectionActivity;

/**
 * Simplified Vision Fragment with just a full screen camera button
 * All advanced camera features are moved to WorkingObjectDetectionActivity
 */
public class VisionFragment extends Fragment {

    private static final String TAG = "VisionFragmentSimple";

    // UI Components
    private TextView cameraStatus;
    private Button fullscreenCameraButton;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                           @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_vision, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        initializeViews(view);
        setupFullscreenButton();
        updateCameraStatus();
    }

    private void initializeViews(View view) {
        // Simple UI components
        cameraStatus = view.findViewById(R.id.camera_status);
        fullscreenCameraButton = view.findViewById(R.id.fullscreen_camera_button);
    }

    private void setupFullscreenButton() {
        if (fullscreenCameraButton != null) {
            fullscreenCameraButton.setOnClickListener(v -> launchFullscreenCamera());
        }
    }

    private void launchFullscreenCamera() {
        try {
            Intent intent = new Intent(getActivity(), WorkingObjectDetectionActivity.class);
            startActivity(intent);
        } catch (Exception e) {
            android.util.Log.e(TAG, "Error launching full screen camera: " + e.getMessage());
        }
    }

    private void updateCameraStatus() {
        if (cameraStatus != null) {
            cameraStatus.setText("Background face detection is active\nMini preview running in navbar\n\nTap the button below for full camera features");
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        updateCameraStatus();
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }
}
