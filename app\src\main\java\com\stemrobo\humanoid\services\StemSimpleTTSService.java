package com.stemrobo.humanoid.services;

import android.content.Context;
import android.speech.tts.TextToSpeech;
import android.speech.tts.UtteranceProgressListener;
import android.util.Log;
import com.stemrobo.humanoid.language.LanguageManager;
import java.util.HashMap;
import java.util.Locale;

/**
 * Text-to-Speech Service for STEM Simple Robot
 * Handles speech output for AI responses
 */
public class StemSimpleTTSService implements TextToSpeech.OnInitListener {
    private static final String TAG = "StemSimpleTTSService";
    
    private final Context context;
    private TextToSpeech textToSpeech;
    private boolean isReady = false;
    private boolean isMale = false;
    private TTSCallback ttsCallback;
    private StemSimpleVoiceService voiceService;
    private boolean isSpeaking = false;
    private LanguageManager languageManager;
    
    public interface TTSCallback {
        void onSpeechStarted(String text);
        void onSpeechCompleted(String text);
        void onSpeechError(String error);
    }
    
    public StemSimpleTTSService(Context context) {
        this.context = context;
        this.languageManager = new LanguageManager(context);
        initializeTTS();
    }
    
    private void initializeTTS() {
        textToSpeech = new TextToSpeech(context, this);
    }
    
    @Override
    public void onInit(int status) {
        if (status == TextToSpeech.SUCCESS) {
            // Set language based on LanguageManager
            Locale ttsLocale = languageManager != null ? languageManager.getSpeechLocale() : Locale.US;
            int result = textToSpeech.setLanguage(ttsLocale);

            if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                Log.w(TAG, "TTS language not supported: " + ttsLocale + ", falling back to English");
                // Fallback to English if current language not supported
                result = textToSpeech.setLanguage(Locale.US);
                if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                    Log.e(TAG, "TTS English language not supported");
                    isReady = false;
                    if (ttsCallback != null) {
                        ttsCallback.onSpeechError("Language not supported");
                    }
                    return;
                }
            }

            isReady = true;
            configureTTS();
            Log.d(TAG, "TTS initialized successfully with language: " + ttsLocale);
        } else {
            Log.e(TAG, "TTS initialization failed");
            isReady = false;
            if (ttsCallback != null) {
                ttsCallback.onSpeechError("TTS initialization failed");
            }
        }
    }
    
    private void configureTTS() {
        if (textToSpeech != null && isReady) {
            // Set speech rate (normal speed)
            textToSpeech.setSpeechRate(1.0f);
            
            // Set pitch based on gender preference
            if (isMale) {
                textToSpeech.setPitch(0.8f); // Lower pitch for male voice
            } else {
                textToSpeech.setPitch(1.0f); // Normal pitch for female voice
            }
            
            // Set utterance progress listener
            textToSpeech.setOnUtteranceProgressListener(new UtteranceProgressListener() {
                @Override
                public void onStart(String utteranceId) {
                    Log.d(TAG, "Speech started: " + utteranceId);
                    isSpeaking = true;
                    if (voiceService != null) {
                        voiceService.setTTSSpeaking(true);
                    }
                    if (ttsCallback != null) {
                        ttsCallback.onSpeechStarted(utteranceId);
                    }
                }

                @Override
                public void onDone(String utteranceId) {
                    Log.d(TAG, "Speech completed: " + utteranceId);
                    isSpeaking = false;
                    if (voiceService != null) {
                        voiceService.setTTSSpeaking(false);
                    }
                    if (ttsCallback != null) {
                        ttsCallback.onSpeechCompleted(utteranceId);
                    }
                }

                @Override
                public void onError(String utteranceId) {
                    Log.e(TAG, "Speech error: " + utteranceId);
                    isSpeaking = false;
                    if (voiceService != null) {
                        voiceService.setTTSSpeaking(false);
                    }
                    if (ttsCallback != null) {
                        ttsCallback.onSpeechError("Speech synthesis error");
                    }
                }
            });
        }
    }
    
    public void setTTSCallback(TTSCallback callback) {
        this.ttsCallback = callback;
    }

    public void setVoiceService(StemSimpleVoiceService voiceService) {
        this.voiceService = voiceService;
        if (voiceService != null) {
            voiceService.setTTSStopCallback(() -> {
                Log.d(TAG, "Stop command received - stopping TTS");
                stopSpeaking();
            });
        }
    }
    
    public void setVoiceGender(boolean isMale) {
        this.isMale = isMale;
        if (isReady) {
            configureTTS();
        }
        Log.d(TAG, "Voice gender set to: " + (isMale ? "Male" : "Female"));
    }
    
    public boolean isVoiceMale() {
        return isMale;
    }

    /**
     * Update TTS language based on current language setting
     */
    public void setLanguage(String languageCode) {
        if (isReady && languageManager != null) {
            languageManager.setCurrentLanguage(languageCode);
            Locale ttsLocale = languageManager.getSpeechLocale();
            int result = textToSpeech.setLanguage(ttsLocale);

            if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                Log.w(TAG, "TTS language not supported: " + ttsLocale + ", falling back to English");
                textToSpeech.setLanguage(Locale.US);
            } else {
                Log.d(TAG, "TTS language updated to: " + ttsLocale);
            }
        }
    }
    
    public void speak(String text) {
        if (!isReady) {
            Log.w(TAG, "TTS not ready, cannot speak: " + text);
            if (ttsCallback != null) {
                ttsCallback.onSpeechError("TTS not ready");
            }
            return;
        }
        
        if (text == null || text.trim().isEmpty()) {
            Log.w(TAG, "Empty text provided for speech");
            return;
        }
        
        try {
            // Create parameters for speech
            HashMap<String, String> params = new HashMap<>();
            params.put(TextToSpeech.Engine.KEY_PARAM_UTTERANCE_ID, text);
            
            // Notify VoiceService that TTS is starting
            isSpeaking = true;
            if (voiceService != null) {
                voiceService.setTTSSpeaking(true);
            }

            // Speak the text
            int result = textToSpeech.speak(text, TextToSpeech.QUEUE_FLUSH, params);

            if (result == TextToSpeech.ERROR) {
                Log.e(TAG, "Error speaking text: " + text);
                isSpeaking = false;
                if (voiceService != null) {
                    voiceService.setTTSSpeaking(false);
                }
                if (ttsCallback != null) {
                    ttsCallback.onSpeechError("Failed to speak text");
                }
            } else {
                Log.d(TAG, "Speaking: " + text);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Exception while speaking", e);
            if (ttsCallback != null) {
                ttsCallback.onSpeechError("Exception: " + e.getMessage());
            }
        }
    }
    
    public void speakQueued(String text) {
        if (!isReady) {
            Log.w(TAG, "TTS not ready, cannot speak: " + text);
            if (ttsCallback != null) {
                ttsCallback.onSpeechError("TTS not ready");
            }
            return;
        }
        
        if (text == null || text.trim().isEmpty()) {
            Log.w(TAG, "Empty text provided for speech");
            return;
        }
        
        try {
            // Create parameters for speech
            HashMap<String, String> params = new HashMap<>();
            params.put(TextToSpeech.Engine.KEY_PARAM_UTTERANCE_ID, text);
            
            // Add to queue instead of replacing
            int result = textToSpeech.speak(text, TextToSpeech.QUEUE_ADD, params);
            
            if (result == TextToSpeech.ERROR) {
                Log.e(TAG, "Error queuing speech: " + text);
                if (ttsCallback != null) {
                    ttsCallback.onSpeechError("Failed to queue speech");
                }
            } else {
                Log.d(TAG, "Queued speech: " + text);
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Exception while queuing speech", e);
            if (ttsCallback != null) {
                ttsCallback.onSpeechError("Exception: " + e.getMessage());
            }
        }
    }

    public void stopSpeaking() {
        if (textToSpeech != null && isSpeaking) {
            Log.d(TAG, "Stopping TTS speech");
            textToSpeech.stop();
            isSpeaking = false;
            if (voiceService != null) {
                voiceService.setTTSSpeaking(false);
            }
            if (ttsCallback != null) {
                ttsCallback.onSpeechCompleted("stopped");
            }
        }
    }

    public void stop() {
        if (textToSpeech != null) {
            textToSpeech.stop();
            Log.d(TAG, "TTS stopped");
        }
    }
    
    public boolean isSpeaking() {
        return textToSpeech != null && textToSpeech.isSpeaking();
    }
    
    public boolean isReady() {
        return isReady;
    }
    
    public void setSpeechRate(float rate) {
        if (textToSpeech != null && isReady) {
            textToSpeech.setSpeechRate(rate);
            Log.d(TAG, "Speech rate set to: " + rate);
        }
    }
    
    public void setPitch(float pitch) {
        if (textToSpeech != null && isReady) {
            textToSpeech.setPitch(pitch);
            Log.d(TAG, "Speech pitch set to: " + pitch);
        }
    }
    
    public void cleanup() {
        if (textToSpeech != null) {
            textToSpeech.stop();
            textToSpeech.shutdown();
            textToSpeech = null;
        }
        isReady = false;
        Log.d(TAG, "TTS service cleaned up");
    }
}
