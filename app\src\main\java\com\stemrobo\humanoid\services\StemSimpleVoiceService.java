package com.stemrobo.humanoid.services;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.speech.RecognitionListener;
import android.speech.RecognizerIntent;
import android.speech.SpeechRecognizer;
import android.util.Log;
import com.stemrobo.humanoid.language.LanguageManager;
import java.util.ArrayList;
import java.util.Locale;

/**
 * Voice Recognition Service for STEM Simple Robot
 * Handles speech-to-text conversion for AI commands
 */
public class StemSimpleVoiceService implements RecognitionListener {
    private static final String TAG = "StemSimpleVoiceService";
    
    private final Context context;
    private SpeechRecognizer speechRecognizer;
    private Intent recognizerIntent;
    private VoiceCallback voiceCallback;
    private LanguageManager languageManager;
    private boolean isListening = false;
    private boolean continuousListening = false;
    private boolean autoVoiceEnabled = true;
    private String wakeWord = "hey robot";
    private long lastRecognitionTime = 0;
    private static final long RECOGNITION_RESTART_DELAY = 1000; // 1 second

    // TTS feedback prevention
    private boolean isTTSSpeaking = false;
    private boolean isTranscriptionMode = false;
    private static final String[] STOP_WORDS = {"stop", "halt", "pause", "quiet", "silence", "enough"};
    private TTSStopCallback ttsStopCallback;

    // Stop command feedback prevention
    private String lastProcessedCommand = "";
    private long lastStopCommandTime = 0;
    private static final long STOP_COMMAND_COOLDOWN = 3000; // 3 seconds cooldown
    
    public interface VoiceCallback {
        void onVoiceResult(String text);
        void onVoiceError(String error);
        void onListeningStarted();
        void onListeningStopped();
    }

    public interface TTSStopCallback {
        void onStopRequested();
    }
    
    public StemSimpleVoiceService(Context context) {
        this.context = context;
        this.languageManager = new LanguageManager(context);
        initializeSpeechRecognizer();
    }
    
    private void initializeSpeechRecognizer() {
        if (SpeechRecognizer.isRecognitionAvailable(context)) {
            speechRecognizer = SpeechRecognizer.createSpeechRecognizer(context);
            speechRecognizer.setRecognitionListener(this);
            
            recognizerIntent = new Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH);
            recognizerIntent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM);

            // Set language based on LanguageManager
            Locale speechLocale = languageManager != null ? languageManager.getSpeechLocale() : Locale.getDefault();
            recognizerIntent.putExtra(RecognizerIntent.EXTRA_LANGUAGE, speechLocale);
            recognizerIntent.putExtra(RecognizerIntent.EXTRA_LANGUAGE_PREFERENCE, speechLocale);

            recognizerIntent.putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true);
            recognizerIntent.putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 1);

            Log.d(TAG, "Speech recognizer set to language: " + speechLocale);
            
            Log.d(TAG, "Speech recognizer initialized");
        } else {
            Log.e(TAG, "Speech recognition not available");
        }
    }
    
    public void setVoiceCallback(VoiceCallback callback) {
        this.voiceCallback = callback;
    }
    
    public void setWakeWord(String wakeWord) {
        this.wakeWord = wakeWord.toLowerCase();
        Log.d(TAG, "Wake word set to: " + this.wakeWord);
    }

    public void setLanguage(String languageCode) {
        Log.d(TAG, "Setting voice recognition language to: " + languageCode);
        if (languageManager != null) {
            languageManager.setCurrentLanguage(languageCode);
        }

        // Stop current listening before re-initializing
        boolean wasListening = isListening;
        if (wasListening) {
            stopListening();
        }

        // Re-initialize the speech recognizer with the new language
        if (speechRecognizer != null) {
            speechRecognizer.destroy();
        }
        initializeSpeechRecognizer();

        // Restart listening if it was active before
        if (wasListening) {
            startListening();
        }
        Log.d(TAG, "Voice recognition language updated successfully.");
    }

    public void setAutoVoiceEnabled(boolean enabled) {
        this.autoVoiceEnabled = enabled;
        Log.d(TAG, "Auto voice recognition " + (enabled ? "enabled" : "disabled"));

        if (enabled && !isListening) {
            startContinuousListening();
        } else if (!enabled && isListening) {
            stopListening();
        }
    }

    public boolean isAutoVoiceEnabled() {
        return autoVoiceEnabled;
    }

    // TTS feedback prevention methods
    public void setTTSStopCallback(TTSStopCallback callback) {
        this.ttsStopCallback = callback;
    }

    public void setTTSSpeaking(boolean speaking) {
        this.isTTSSpeaking = speaking;
        if (speaking) {
            // Switch to transcription mode during TTS
            this.isTranscriptionMode = true;
            Log.d(TAG, "TTS started - switching to transcription mode");
        } else {
            // Return to normal mode after TTS
            this.isTranscriptionMode = false;
            Log.d(TAG, "TTS stopped - returning to normal mode");
        }
    }

    public void startContinuousListening() {
        if (!autoVoiceEnabled) {
            Log.d(TAG, "Auto voice recognition is disabled");
            return;
        }

        continuousListening = true;
        startListening();
        Log.d(TAG, "Continuous listening started");
    }

    public void stopContinuousListening() {
        continuousListening = false;
        stopListening();
        Log.d(TAG, "Continuous listening stopped");
    }
    
    public String getWakeWord() {
        return wakeWord;
    }
    
    public void startListening() {
        if (speechRecognizer == null) {
            Log.e(TAG, "Speech recognizer not available");
            if (voiceCallback != null) {
                voiceCallback.onVoiceError("Speech recognition not available");
            }
            return;
        }
        
        if (isListening) {
            Log.w(TAG, "Already listening");
            return;
        }
        
        try {
            speechRecognizer.startListening(recognizerIntent);
            isListening = true;
            Log.d(TAG, "Started listening for voice input");
            
            if (voiceCallback != null) {
                voiceCallback.onListeningStarted();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error starting voice recognition", e);
            if (voiceCallback != null) {
                voiceCallback.onVoiceError("Failed to start listening: " + e.getMessage());
            }
        }
    }
    
    public void stopListening() {
        if (speechRecognizer != null && isListening) {
            speechRecognizer.stopListening();
            isListening = false;
            Log.d(TAG, "Stopped listening for voice input");
            
            if (voiceCallback != null) {
                voiceCallback.onListeningStopped();
            }
        }
    }
    
    public boolean isListening() {
        return isListening;
    }
    
    // RecognitionListener implementation
    @Override
    public void onReadyForSpeech(Bundle params) {
        Log.d(TAG, "Ready for speech");
    }
    
    @Override
    public void onBeginningOfSpeech() {
        Log.d(TAG, "Beginning of speech");
    }
    
    @Override
    public void onRmsChanged(float rmsdB) {
        // Audio level changed - can be used for visual feedback
    }
    
    @Override
    public void onBufferReceived(byte[] buffer) {
        // Audio buffer received
    }
    
    @Override
    public void onEndOfSpeech() {
        Log.d(TAG, "End of speech");
        isListening = false;
        lastRecognitionTime = System.currentTimeMillis();
    }
    
    @Override
    public void onError(int error) {
        String errorMessage = getErrorMessage(error);
        Log.e(TAG, "Speech recognition error: " + errorMessage);

        isListening = false;

        if (voiceCallback != null) {
            voiceCallback.onVoiceError(errorMessage);
            voiceCallback.onListeningStopped();
        }

        // Restart listening if in continuous mode and not a critical error
        if (continuousListening && autoVoiceEnabled && !isCriticalError(error)) {
            // Wait a bit before restarting to avoid rapid restart loops
            new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                if (continuousListening && autoVoiceEnabled) {
                    Log.d(TAG, "Restarting speech recognition after error");
                    startListening();
                }
            }, RECOGNITION_RESTART_DELAY);
        }
    }

    private boolean isCriticalError(int error) {
        return error == SpeechRecognizer.ERROR_AUDIO ||
               error == SpeechRecognizer.ERROR_CLIENT ||
               error == SpeechRecognizer.ERROR_INSUFFICIENT_PERMISSIONS ||
               error == SpeechRecognizer.ERROR_RECOGNIZER_BUSY;
    }

    @Override
    public void onResults(Bundle results) {
        ArrayList<String> matches = results.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION);

        if (matches != null && !matches.isEmpty()) {
            String recognizedText = matches.get(0);
            Log.d(TAG, "Speech recognized: " + recognizedText);

            // Handle transcription mode (during TTS)
            if (isTranscriptionMode && isTTSSpeaking) {
                if (containsStopWord(recognizedText)) {
                    Log.d(TAG, "Stop word detected during TTS: " + recognizedText);
                    lastStopCommandTime = System.currentTimeMillis();
                    lastProcessedCommand = "stop";
                    if (ttsStopCallback != null) {
                        ttsStopCallback.onStopRequested();
                    }
                }
                // Don't process as normal voice command during TTS
                restartListeningIfNeeded();
                return;
            }

            // Prevent stop command feedback loop
            if (containsStopWord(recognizedText)) {
                long timeSinceLastStop = System.currentTimeMillis() - lastStopCommandTime;
                if (timeSinceLastStop < STOP_COMMAND_COOLDOWN && lastProcessedCommand.equals("stop")) {
                    Log.d(TAG, "Ignoring stop command feedback loop: " + recognizedText);
                    restartListeningIfNeeded();
                    return;
                }
            }

            // Normal voice processing mode
            // Check for wake word
            if (recognizedText.toLowerCase().contains(wakeWord)) {
                // Remove wake word from the command
                String command = recognizedText.toLowerCase().replace(wakeWord, "").trim();
                if (!command.isEmpty()) {
                    lastProcessedCommand = command.toLowerCase();
                    if (voiceCallback != null) {
                        voiceCallback.onVoiceResult(command);
                    }
                } else {
                    // Just wake word, start listening for command
                    lastProcessedCommand = "";
                    if (voiceCallback != null) {
                        voiceCallback.onVoiceResult(""); // Empty command indicates wake word detected
                    }
                }
            } else {
                // No wake word, pass full text
                lastProcessedCommand = recognizedText.toLowerCase();
                if (voiceCallback != null) {
                    voiceCallback.onVoiceResult(recognizedText);
                }
            }
        } else {
            Log.w(TAG, "No speech recognition results");
            if (voiceCallback != null) {
                voiceCallback.onVoiceError("No speech recognized");
            }
        }

        isListening = false;
        if (voiceCallback != null) {
            voiceCallback.onListeningStopped();
        }

        restartListeningIfNeeded();
    }

    private boolean containsStopWord(String text) {
        String lowerText = text.toLowerCase();
        for (String stopWord : STOP_WORDS) {
            if (lowerText.contains(stopWord)) {
                return true;
            }
        }
        return false;
    }

    private void restartListeningIfNeeded() {
        // Restart listening if in continuous mode
        if (continuousListening && autoVoiceEnabled) {
            new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
                if (continuousListening && autoVoiceEnabled) {
                    Log.d(TAG, "Restarting speech recognition for continuous listening");
                    startListening();
                }
            }, RECOGNITION_RESTART_DELAY);
        }
    }

    @Override
    public void onPartialResults(Bundle partialResults) {
        ArrayList<String> matches = partialResults.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION);
        if (matches != null && !matches.isEmpty()) {
            String partialText = matches.get(0);
            Log.d(TAG, "Partial result: " + partialText);

            // Real-time stop word detection during TTS
            if (isTranscriptionMode && isTTSSpeaking && containsStopWord(partialText)) {
                Log.d(TAG, "Stop word detected in partial result during TTS: " + partialText);
                if (ttsStopCallback != null) {
                    ttsStopCallback.onStopRequested();
                }
            }
        }
    }

    @Override
    public void onEvent(int eventType, Bundle params) {
        Log.d(TAG, "Speech recognition event: " + eventType);
    }

    private String getErrorMessage(int errorCode) {
        switch (errorCode) {
            case SpeechRecognizer.ERROR_AUDIO:
                return "Audio recording error";
            case SpeechRecognizer.ERROR_CLIENT:
                return "Client side error";
            case SpeechRecognizer.ERROR_INSUFFICIENT_PERMISSIONS:
                return "Insufficient permissions";
            case SpeechRecognizer.ERROR_NETWORK:
                return "Network error";
            case SpeechRecognizer.ERROR_NETWORK_TIMEOUT:
                return "Network timeout";
            case SpeechRecognizer.ERROR_NO_MATCH:
                return "No speech match";
            case SpeechRecognizer.ERROR_RECOGNIZER_BUSY:
                return "Recognition service busy";
            case SpeechRecognizer.ERROR_SERVER:
                return "Server error";
            case SpeechRecognizer.ERROR_SPEECH_TIMEOUT:
                return "No speech input";
            default:
                return "Unknown error (" + errorCode + ")";
        }
    }

    public void cleanup() {
        if (speechRecognizer != null) {
            speechRecognizer.destroy();
            speechRecognizer = null;
        }
        isListening = false;
        Log.d(TAG, "Voice service cleaned up");
    }
}
