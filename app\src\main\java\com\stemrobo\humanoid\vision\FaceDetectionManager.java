package com.stemrobo.humanoid.vision;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Rect;
import android.util.Log;
import android.util.Size;
import android.view.Surface;
import androidx.annotation.NonNull;
import androidx.camera.core.CameraSelector;
import androidx.camera.core.ImageAnalysis;
import androidx.camera.core.ImageProxy;
import androidx.camera.core.Preview;
import androidx.camera.lifecycle.ProcessCameraProvider;
import androidx.camera.view.PreviewView;
import androidx.lifecycle.LifecycleOwner;
import com.google.mlkit.vision.common.InputImage;
import com.google.mlkit.vision.face.Face;
import com.google.mlkit.vision.face.FaceDetection;
import com.google.mlkit.vision.face.FaceDetector;
import com.google.mlkit.vision.face.FaceDetectorOptions;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Face Detection Manager using ML Kit and CameraX
 * Based on working ML Kit vision reference implementation
 */
public class FaceDetectionManager {

    private static final String TAG = "FaceDetectionManager";

    // Singleton instance for Smart Greeting integration
    private static FaceDetectionManager instance;

    private final Context context;
    private final LifecycleOwner lifecycleOwner;
    
    // Camera components
    private PreviewView previewView;
    private FaceBoxOverlay faceBoxOverlay;
    private ProcessCameraProvider cameraProvider;
    private Preview cameraPreview;
    private ImageAnalysis imageAnalysis;
    private CameraSelector cameraSelector;
    
    // ML Kit components
    private FaceDetector faceDetector;
    private final ExecutorService cameraExecutor;
    
    // State
    private boolean isBackCamera = false; // Start with front camera
    private boolean isDetectionEnabled = false;
    private FaceDetectionCallback callback;
    private int currentFaceCount = 0; // Track current face count for Smart Greeting

    // ENHANCED FACE TRACKING STABILITY
    private final java.util.concurrent.ConcurrentHashMap<Integer, FaceTrackingInfo> faceTrackingMap = new java.util.concurrent.ConcurrentHashMap<>();
    private final java.util.concurrent.ConcurrentHashMap<String, Long> personStabilityMap = new java.util.concurrent.ConcurrentHashMap<>();
    private static final long FACE_STABILITY_DURATION = 1000; // 1 second for stability
    private static final int MIN_FACE_QUALITY_SCORE = 70; // Minimum quality score (0-100)
    private static final float MAX_FACE_MOVEMENT_THRESHOLD = 50.0f; // Max movement between frames

    // Face tracking information class
    private static class FaceTrackingInfo {
        final Integer trackingId;
        final String personId;
        long firstDetectionTime; // Made non-final to allow reset
        long lastSeenTime;
        android.graphics.RectF lastBoundingBox;
        float lastQualityScore;
        int consecutiveDetections;
        boolean isStable;

        FaceTrackingInfo(Integer trackingId, String personId, android.graphics.RectF boundingBox, float qualityScore) {
            this.trackingId = trackingId;
            this.personId = personId;
            this.firstDetectionTime = System.currentTimeMillis();
            this.lastSeenTime = this.firstDetectionTime;
            this.lastBoundingBox = new android.graphics.RectF(boundingBox);
            this.lastQualityScore = qualityScore;
            this.consecutiveDetections = 1;
            this.isStable = false;
        }

        void updateTracking(android.graphics.RectF boundingBox, float qualityScore) {
            this.lastSeenTime = System.currentTimeMillis();
            this.lastBoundingBox = new android.graphics.RectF(boundingBox);
            this.lastQualityScore = qualityScore;
            this.consecutiveDetections++;

            // Check if face is now stable
            long detectionDuration = this.lastSeenTime - this.firstDetectionTime;
            if (detectionDuration >= FACE_STABILITY_DURATION && this.consecutiveDetections >= 3) {
                this.isStable = true;
            }
        }

        boolean hasMovedSignificantly(android.graphics.RectF newBoundingBox) {
            if (lastBoundingBox == null) return false;

            float centerXDiff = Math.abs(newBoundingBox.centerX() - lastBoundingBox.centerX());
            float centerYDiff = Math.abs(newBoundingBox.centerY() - lastBoundingBox.centerY());

            return (centerXDiff > MAX_FACE_MOVEMENT_THRESHOLD || centerYDiff > MAX_FACE_MOVEMENT_THRESHOLD);
        }
    }
    
    // Callback interface
    public interface FaceDetectionCallback {
        void onFacesDetected(List<Face> faces, Bitmap cameraBitmap);
        void onNoFacesDetected();
        void onDetectionError(Exception error);
        void onCameraFrame(Bitmap cameraBitmap); // New callback for every camera frame
    }
    
    public FaceDetectionManager(Context context, LifecycleOwner lifecycleOwner) {
        this.context = context;
        this.lifecycleOwner = lifecycleOwner;
        this.cameraExecutor = Executors.newSingleThreadExecutor();
        
        initializeFaceDetector();
    }
    
    /**
     * Initialize ML Kit face detector with optimized settings (like stem-simple)
     */
    private void initializeFaceDetector() {
        FaceDetectorOptions options = new FaceDetectorOptions.Builder()
            .setPerformanceMode(FaceDetectorOptions.PERFORMANCE_MODE_FAST)
            .setContourMode(FaceDetectorOptions.CONTOUR_MODE_NONE)
            .setClassificationMode(FaceDetectorOptions.CLASSIFICATION_MODE_NONE) // Disabled for better performance
            .setLandmarkMode(FaceDetectorOptions.LANDMARK_MODE_NONE)
            .setMinFaceSize(0.1f) // Smaller minimum face size like stem-simple
            .enableTracking() // Enable face tracking for better performance
            .build();

        faceDetector = FaceDetection.getClient(options);
        Log.d(TAG, "Face detector initialized with optimized settings for performance");
    }
    
    /**
     * Set preview view and overlay
     */
    public void setPreviewView(PreviewView previewView, FaceBoxOverlay overlay) {
        this.previewView = previewView;
        this.faceBoxOverlay = overlay;
    }
    
    /**
     * Set face detection callback
     */
    public void setCallback(FaceDetectionCallback callback) {
        this.callback = callback;
    }
    
    /**
     * Start camera with face detection
     */
    public void startCamera(ProcessCameraProvider cameraProvider) {
        this.cameraProvider = cameraProvider;
        
        // Select camera
        cameraSelector = isBackCamera ? 
            CameraSelector.DEFAULT_BACK_CAMERA : CameraSelector.DEFAULT_FRONT_CAMERA;
        
        bindCameraUseCases();
        Log.d(TAG, "Camera started with " + (isBackCamera ? "back" : "front") + " camera");
    }
    
    /**
     * Bind camera use cases (preview and analysis)
     */
    private void bindCameraUseCases() {
        if (cameraProvider == null || previewView == null) {
            Log.e(TAG, "Camera provider or preview view not available");
            return;
        }
        
        try {
            // Unbind all use cases before rebinding
            cameraProvider.unbindAll();
            
            // Set up preview
            bindCameraPreview();
            
            // Set up image analysis for face detection
            bindImageAnalysis();
            
        } catch (Exception e) {
            Log.e(TAG, "Error binding camera use cases", e);
            if (callback != null) {
                callback.onDetectionError(e);
            }
        }
    }
    
    /**
     * Bind camera preview - Hidden minimal preview for performance (like stem-simple)
     */
    private void bindCameraPreview() {
        // Build preview with current display rotation for proper orientation
        cameraPreview = new Preview.Builder()
            .setTargetRotation(previewView.getDisplay().getRotation()) // Use current display rotation
            .build();

        // Set surface provider (preview is hidden but functional)
        cameraPreview.setSurfaceProvider(previewView.getSurfaceProvider());

        try {
            cameraProvider.bindToLifecycle(lifecycleOwner, cameraSelector, cameraPreview);
            Log.d(TAG, "Hidden camera preview bound for background face detection");
        } catch (IllegalStateException e) {
            Log.e(TAG, "IllegalStateException binding preview: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            Log.e(TAG, "IllegalArgumentException binding preview: " + e.getMessage());
        }
    }




    
    /**
     * Bind image analysis - Optimized for background face detection (like stem-simple)
     */
    private void bindImageAnalysis() {
        imageAnalysis = new ImageAnalysis.Builder()
            .setTargetRotation(previewView.getDisplay().getRotation()) // Use current display rotation
            .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
            .build();

        imageAnalysis.setAnalyzer(cameraExecutor, this::processImageProxy);

        try {
            cameraProvider.bindToLifecycle(lifecycleOwner, cameraSelector, imageAnalysis);
            Log.d(TAG, "Image analysis bound for optimized background face detection");
        } catch (IllegalStateException e) {
            Log.e(TAG, "IllegalStateException binding analysis: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            Log.e(TAG, "IllegalArgumentException binding analysis: " + e.getMessage());
        }
    }

    
    /**
     * Process camera frames for face detection
     */
    @SuppressLint("UnsafeOptInUsageError")
    private void processImageProxy(ImageProxy imageProxy) {
        try {
            if (imageProxy.getImage() == null) {
                imageProxy.close();
                return;
            }

            // Convert camera image to bitmap for all processing
            Bitmap cameraBitmap = convertImageProxyToBitmap(imageProxy.getImage());

            // ALWAYS call the camera frame callback for object detection
            if (callback != null) {
                callback.onCameraFrame(cameraBitmap);
            }

            // Only do face detection if enabled
            if (isDetectionEnabled) {
                InputImage inputImage = InputImage.fromMediaImage(
                    imageProxy.getImage(),
                    imageProxy.getImageInfo().getRotationDegrees()
                );

                faceDetector.process(inputImage)
                    .addOnSuccessListener(faces -> {
                        handleFaceDetectionSuccess(faces, imageProxy.getImage().getCropRect(), cameraBitmap);
                    })
                    .addOnFailureListener(e -> {
                        Log.e(TAG, "Face detection failed", e);
                        if (callback != null) {
                            callback.onDetectionError(e);
                        }
                    })
                    .addOnCompleteListener(task -> {
                        imageProxy.close();
                    });
            } else {
                // Close immediately if face detection is disabled
                imageProxy.close();
            }

        } catch (Exception e) {
            Log.e(TAG, "Error processing image proxy", e);
            imageProxy.close();
        }
    }

    /**
     * Convert ImageProxy to Bitmap for face recognition
     */
    private Bitmap convertImageProxyToBitmap(android.media.Image image) {
        try {
            // Get image planes
            android.media.Image.Plane[] planes = image.getPlanes();
            android.media.Image.Plane yPlane = planes[0];
            android.media.Image.Plane uPlane = planes[1];
            android.media.Image.Plane vPlane = planes[2];

            int ySize = yPlane.getBuffer().remaining();
            int uSize = uPlane.getBuffer().remaining();
            int vSize = vPlane.getBuffer().remaining();

            byte[] nv21 = new byte[ySize + uSize + vSize];

            // Copy Y plane
            yPlane.getBuffer().get(nv21, 0, ySize);
            
            // Copy UV planes
            byte[] uvPixelStride = new byte[uSize];
            uPlane.getBuffer().get(uvPixelStride);
            
            if (uPlane.getPixelStride() == 1) {
                System.arraycopy(uvPixelStride, 0, nv21, ySize, uSize);
            } else {
                // Handle pixel stride > 1
                for (int i = 0; i < uSize; i += uPlane.getPixelStride()) {
                    nv21[ySize + i] = uvPixelStride[i];
                }
            }

            // Convert YUV to RGB
            int width = image.getWidth();
            int height = image.getHeight();
            int[] argb = new int[width * height];
            
            // Simple YUV to RGB conversion
            for (int i = 0; i < height; i++) {
                for (int j = 0; j < width; j++) {
                    int index = i * width + j;
                    int y = nv21[index] & 0xFF;
                    
                    // Simple grayscale conversion for now
                    int rgb = (y << 16) | (y << 8) | y;
                    argb[index] = 0xFF000000 | rgb;
                }
            }

            return Bitmap.createBitmap(argb, width, height, Bitmap.Config.ARGB_8888);
            
        } catch (Exception e) {
            Log.e(TAG, "Error converting image to bitmap", e);
            // Return a placeholder bitmap with correct dimensions
            return Bitmap.createBitmap(image.getWidth(), image.getHeight(), Bitmap.Config.ARGB_8888);
        }
    }

    /**
     * Handle successful face detection - ENHANCED WITH STABILITY TRACKING
     */
    private void handleFaceDetectionSuccess(List<Face> faces, Rect imageRect, Bitmap cameraBitmap) {
        // Clear previous face boxes
        if (faceBoxOverlay != null) {
            faceBoxOverlay.clear();
        }

        // Process faces with enhanced tracking
        List<Face> stableFaces = processStableFaces(faces, imageRect);

        if (faces.isEmpty()) {
            // Update current face count for Smart Greeting
            currentFaceCount = 0;

            // Update MainActivity status bar directly for always-on functionality
            updateMainActivityStatusBar(currentFaceCount);

            if (callback != null) {
                callback.onNoFacesDetected();
            }
        } else {
            // Draw face boxes with expression analysis for stable faces
            if (faceBoxOverlay != null) {
                // Update overlay with camera information for better coordinate transformation
                faceBoxOverlay.setCameraInfo(!isBackCamera, imageRect.width(), imageRect.height());

                for (Face face : stableFaces) {
                    FaceBox faceBox = new FaceBox(faceBoxOverlay, face, imageRect);

                    // Analyze expression for stable faces
                    String expression = faceBox.analyzeExpression();
                    faceBox.setExpression(expression);
                    android.util.Log.d(TAG, "Stable face expression: " + expression);

                    faceBoxOverlay.add(faceBox);
                }

                // Update face count with all detected faces (not just stable ones)
                faceBoxOverlay.setFaceCount(faces.size());
            }

            if (callback != null) {
                // Pass stable faces to callback for processing
                callback.onFacesDetected(stableFaces, cameraBitmap);
            }

            android.util.Log.d(TAG, "Detected " + faces.size() + " face(s), " + stableFaces.size() + " stable");

            // Update current face count for Smart Greeting (use all faces for count)
            currentFaceCount = faces.size();

            // Update MainActivity status bar directly for always-on functionality
            updateMainActivityStatusBar(currentFaceCount);
        }

        // Clean up old tracking data
        cleanupOldTrackingData();
    }

    /**
     * Find the closest face (largest bounding box area) for expression analysis
     */
    private Face findClosestFace(List<Face> faces) {
        if (faces == null || faces.isEmpty()) {
            return null;
        }

        Face closestFace = null;
        float largestArea = 0;

        for (Face face : faces) {
            Rect boundingBox = face.getBoundingBox();
            float area = boundingBox.width() * boundingBox.height();

            if (area > largestArea) {
                largestArea = area;
                closestFace = face;
            }
        }

        return closestFace;
    }
    
    /**
     * Switch between front and back camera
     */
    public void switchCamera() {
        isBackCamera = !isBackCamera;
        Log.d(TAG, "Switching to " + (isBackCamera ? "back" : "front") + " camera");

        if (cameraProvider != null) {
            bindCameraUseCases();
        }
    }

    /**
     * Handle orientation changes for smooth camera preview
     * Call this method when device orientation changes
     */
    public void handleOrientationChange() {
        if (cameraProvider != null && previewView != null) {
            Log.d(TAG, "Handling orientation change - rebinding camera with new rotation");

            // Unbind all use cases first
            cameraProvider.unbindAll();

            // Rebind camera use cases with new orientation
            bindCameraUseCases();
        }
    }
    
    /**
     * Enable or disable face detection
     */
    public void setDetectionEnabled(boolean enabled) {
        this.isDetectionEnabled = enabled;
        Log.d(TAG, "Face detection " + (enabled ? "enabled" : "disabled"));
        
        if (!enabled && faceBoxOverlay != null) {
            faceBoxOverlay.clear();
        }
    }
    
    /**
     * Check if using back camera
     */
    public boolean isBackCamera() {
        return isBackCamera;
    }
    
    /**
     * Check if detection is enabled
     */
    public boolean isDetectionEnabled() {
        return isDetectionEnabled;
    }

    /**
     * Get current face count for Smart Greeting integration
     */
    public int getCurrentFaceCount() {
        return currentFaceCount;
    }

    /**
     * Update MainActivity status bar with face count
     */
    private void updateMainActivityStatusBar(int faceCount) {
        try {
            // Get MainActivity instance if available
            if (context instanceof com.stemrobo.humanoid.MainActivity) {
                com.stemrobo.humanoid.MainActivity mainActivity = (com.stemrobo.humanoid.MainActivity) context;

                // Update face count display in status bar
                mainActivity.updateFaceCountDisplay(faceCount, true);

                System.out.println("FaceDetectionManager: Updated status bar with " + faceCount + " faces");
            }
        } catch (Exception e) {
            System.err.println("FaceDetectionManager: Error updating status bar: " + e.getMessage());
        }
    }
    
    /**
     * Stop camera and cleanup
     */
    public void stopCamera() {
        if (cameraProvider != null) {
            cameraProvider.unbindAll();
        }
        
        if (faceBoxOverlay != null) {
            faceBoxOverlay.clear();
        }
        
        Log.d(TAG, "Camera stopped");
    }
    
    /**
     * Cleanup resources
     */
    public void cleanup() {
        stopCamera();
        
        if (cameraExecutor != null && !cameraExecutor.isShutdown()) {
            cameraExecutor.shutdown();
        }
        
        Log.d(TAG, "Face detection manager cleaned up");
    }

    /**
     * Get singleton instance for Smart Greeting integration
     */
    public static FaceDetectionManager getInstance() {
        return instance;
    }

    /**
     * Set singleton instance for Smart Greeting integration
     */
    public static void setInstance(FaceDetectionManager manager) {
        instance = manager;
    }

    /**
     * Process faces with enhanced stability tracking
     */
    private List<Face> processStableFaces(List<Face> faces, Rect imageRect) {
        List<Face> stableFaces = new ArrayList<>();
        long currentTime = System.currentTimeMillis();

        for (Face face : faces) {
            // Calculate face quality score
            float qualityScore = calculateFaceQualityScore(face);

            // Skip low quality faces
            if (qualityScore < MIN_FACE_QUALITY_SCORE) {
                continue;
            }

            // Generate consistent person ID
            String personId = generateConsistentPersonId(face);

            // Get or create tracking info
            Integer trackingId = face.getTrackingId();
            FaceTrackingInfo trackingInfo = faceTrackingMap.get(trackingId);

            android.graphics.RectF currentBoundingBox = new android.graphics.RectF(face.getBoundingBox());

            if (trackingInfo == null) {
                // New face detected
                trackingInfo = new FaceTrackingInfo(trackingId, personId, currentBoundingBox, qualityScore);
                faceTrackingMap.put(trackingId, trackingInfo);
                android.util.Log.d(TAG, "New face tracked: " + trackingId + " (person: " + personId + ")");
            } else {
                // Update existing tracking
                if (!trackingInfo.hasMovedSignificantly(currentBoundingBox)) {
                    trackingInfo.updateTracking(currentBoundingBox, qualityScore);
                } else {
                    // Face moved significantly, reset stability
                    android.util.Log.d(TAG, "Face moved significantly, resetting stability: " + trackingId);
                    trackingInfo.isStable = false;
                    trackingInfo.consecutiveDetections = 1;
                    trackingInfo.firstDetectionTime = currentTime;
                }
            }

            // Add to stable faces if tracking is stable
            if (trackingInfo.isStable) {
                stableFaces.add(face);

                // Update person stability map
                personStabilityMap.put(personId, currentTime);
            }
        }

        return stableFaces;
    }

    /**
     * Calculate face quality score (0-100)
     */
    private float calculateFaceQualityScore(Face face) {
        android.graphics.Rect bounds = face.getBoundingBox();
        if (bounds == null) return 0.0f;

        float score = 0.0f;

        // Size score (30% weight) - larger faces are better
        float area = bounds.width() * bounds.height();
        float sizeScore = Math.min(area / 10000.0f, 1.0f) * 30.0f;
        score += sizeScore;

        // Angle score (40% weight) - faces looking forward are better
        float angleY = Math.abs(face.getHeadEulerAngleY());
        float angleZ = Math.abs(face.getHeadEulerAngleZ());
        float angleScore = Math.max(0, 1.0f - (angleY + angleZ) / 60.0f) * 40.0f;
        score += angleScore;

        // Position score (20% weight) - faces in center are better
        float centerX = bounds.centerX();
        float positionScore = Math.max(0, 1.0f - Math.abs(centerX - 640) / 640.0f) * 20.0f;
        score += positionScore;

        // Tracking confidence (10% weight)
        if (face.getTrackingId() != null) {
            score += 10.0f; // Bonus for having tracking ID
        }

        return Math.min(score, 100.0f);
    }

    /**
     * Generate consistent person ID based on face characteristics
     */
    private String generateConsistentPersonId(Face face) {
        android.graphics.Rect bounds = face.getBoundingBox();
        if (bounds == null) return "unknown_" + System.currentTimeMillis();

        // Create a more stable person ID based on face characteristics
        int sizeCategory = (bounds.width() * bounds.height()) / 1000; // Size category
        int positionCategory = bounds.centerX() / 100; // Position category
        float angleCategory = Math.round(face.getHeadEulerAngleY() / 15.0f); // Angle category

        return String.format("person_%d_%d_%.0f", sizeCategory, positionCategory, angleCategory);
    }

    /**
     * Clean up old tracking data
     */
    private void cleanupOldTrackingData() {
        long currentTime = System.currentTimeMillis();
        long maxAge = 5000; // 5 seconds

        // Remove old face tracking data
        faceTrackingMap.entrySet().removeIf(entry ->
            (currentTime - entry.getValue().lastSeenTime) > maxAge);

        // Remove old person stability data
        personStabilityMap.entrySet().removeIf(entry ->
            (currentTime - entry.getValue()) > maxAge);
    }

}
