<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/content_background"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Camera Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardBackgroundColor="@color/control_button_background"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="24dp"
                android:gravity="center">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="📹 Camera &amp; Vision"
                    android:textColor="@color/text_primary"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:gravity="center"
                    android:layout_marginBottom="16dp" />

                <!-- Status Info -->
                <TextView
                    android:id="@+id/camera_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Background face detection is active\nMini preview running in navbar"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    android:gravity="center"
                    android:layout_marginBottom="24dp" />

                <!-- Full Screen Camera Button -->
                <Button
                    android:id="@+id/fullscreen_camera_button"
                    android:layout_width="match_parent"
                    android:layout_height="60dp"
                    android:text="🎥 Switch to Full Screen Camera"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:backgroundTint="@color/robot_primary"
                    android:textColor="@android:color/white"
                    android:layout_marginBottom="16dp" />

                <!-- Quick Info -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Full screen camera includes:\n• Face detection &amp; emotion analysis\n• Face count display\n• Portrait &amp; landscape support\n• Advanced vision features"
                    android:textColor="@color/text_hint"
                    android:textSize="12sp"
                    android:gravity="center"
                    android:lineSpacingExtra="2dp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>



    </LinearLayout>

</ScrollView>
